div.vhst_container_div {
  overflow: hidden;
  display: none;
  border-style: none;
  border-width: 0px;
  padding: 0px;
  position:relative;
}

div.vhst_div_hl,
div.vhst_div_hr,
div.vhst_div_dl,
div.vhst_div_dr,
div.vhst_div_fl,
div.vhst_div_fr {
  position:absolute;
  text-align: left;
  vertical-align: top;
  margin: 0px;
  padding: 0px;
  background-color: #FFFFFF;
}

div.vhst_overflow_hidden {
  overflow: hidden;
}

div.vhst_overflow_scroll_xy {
  overflow: scroll;
}

div.vhst_overflow_scroll_x {
  overflow-x: scroll;
  overflow-y: hidden;
}

div.vhst_overflow_scroll_y {
  overflow-x: hidden;
  overflow-y: scroll;
}

table.vhst_table {
  table-layout: fixed;
  border-collapse: collapse;
  border-style: none;
  border-width: 0px;
  background-color: #FFFFFF;
  margin: 0px;
}

table.vhst_table > thead > tr > th,
table.vhst_table > thead > tr > td,
table.vhst_table > tbody > tr > th,
table.vhst_table > tbody > tr > td {
  border-style: solid;
  /*white-space: nowrap;*/
  /*overflow: hidden;*/
}

table.vhst_border_top_none,
table.vhst_border_top_none > thead > tr > th,
table.vhst_border_top_none > thead > tr > td,
table.vhst_border_top_none > tbody > tr > th,
table.vhst_border_top_none > tbody > tr > td {
  border-top-style: none;
}

table.vhst_border_left_none,
table.vhst_border_left_none > thead > tr > th,
table.vhst_border_left_none > thead > tr > td,
table.vhst_border_left_none > tbody > tr > th,
table.vhst_border_left_none > tbody > tr > td {
  border-left-style: none;
}

