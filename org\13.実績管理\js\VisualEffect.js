
var VisualEffect = Class.create();

VisualEffect.prototype = {
  msec: 100,
  setMsec: function(msec) {
    msec && (this.msec = msec);
  },
  cancel: function() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = undefined;
    }
  }
}

VisualEffect.fadein = function(element, msec) {
  var effect = new VisualEffect.Fade(element);
  effect.from = 0;
  effect.to = 100;
  effect.setMsec(msec);
  effect.start();
};

VisualEffect.fadeout = function(element, msec) {
  var effect = new VisualEffect.Fade(element);
  effect.from = 100;
  effect.to = 0;
  effect.setMsec(msec);
  effect.start();
};

VisualEffect.clipin = function(element, dest, msec) {
  var effect = new VisualEffect.Clip(element);
  effect.from = dest;
  effect.to = VisualEffect.Rectangle.whole(element);
  effect.setMsec(msec);
  effect.start();
};

VisualEffect.clipout = function(element, dest, msec) {
  var effect = new VisualEffect.Clip(element);
  effect.from = VisualEffect.Rectangle.whole(element);
  effect.to = dest;
  effect.setMsec(msec);
  effect.start();
};

VisualEffect.Rectangle = Class.create();

VisualEffect.Rectangle.topLeft = function(element) {
  return [0, 0, 0, 0];
};

VisualEffect.Rectangle.topRight = function(element) {
  return [element.offsetWidth, 0, element.offsetWidth, 0];
};

VisualEffect.Rectangle.bottomLeft = function(element) {
  return [0, element.offsetHeight, 0, element.offsetHeight];
};

VisualEffect.Rectangle.bottomRight = function(element) {
  return [element.offsetWidth, element.offsetHeight, element.offsetWidth, element.offsetHeight];
};

VisualEffect.Rectangle.center = function(element) {
  return [
    Math.floor(element.offsetWidth / 2),
    Math.floor(element.offsetHeight / 2),
    Math.floor(element.offsetWidth / 2),
    Math.floor(element.offsetHeight / 2)
  ];
};

VisualEffect.Rectangle.whole = function(element) {
  return [0, 0, element.offsetWidth, element.offsetHeight];
};

VisualEffect.Fade = Class.create();
Object.extend(VisualEffect.Fade.prototype, VisualEffect.prototype);
Object.extend(VisualEffect.Fade.prototype, {
  initialize: function(element) {
    this.element = element;
    this.from = 100;
    this.to = 0;
    this.interval = 10;
    this.filter = element.style.filter;
  },
  start: function() {
    this.opacity = this.from;
    
    this.delta = (this.to - this.from) / (this.msec / this.interval);
    
    this.action();
  },
  action: function() {
    var opacity = this.opacity + this.delta;
    this.opacity = this.delta >= 0 ? Math.min(opacity, this.to) : Math.max(opacity, this.to);
    
    this.element.style.filter = "Alpha(opacity=" + this.opacity + ");" + this.filter;
    
    if (this.opacity != this.to) {
      this.timer = setTimeout(function() {
        this.action();
      }.bind(this), this.interval);
    }
  }
});

VisualEffect.Clip = Class.create();
Object.extend(VisualEffect.Clip.prototype, VisualEffect.prototype);
Object.extend(VisualEffect.Clip.prototype, {
  initialize: function(element) {
    this.element = element;
    this.from = VisualEffect.Rectangle.whole(element);
    this.to = VisualEffect.Rectangle.center(element);
    this.interval = 10;
  },
  start: function() {
    this.clip = Object.clone(this.from);
     
    this.delta = [];
    for (var i=0;i<4;i++) {
      this.delta[i] = (this.to[i] - this.from[i]) / (this.msec / this.interval);
    }
    
    this.action();
  },
  action: function() {
    for (var i=0;i<4;i++) {
      var clip = this.clip[i] + this.delta[i];
      this.clip[i] = this.delta[i] >= 0 ? Math.min(clip, this.to[i]) : Math.max(clip, this.to[i]);
    }
    
    this.element.style.clip = "rect(" +
    Math.floor(this.clip[1]) + "," +
    Math.floor(this.clip[2]) + "," +
    Math.floor(this.clip[3]) + "," +
    Math.floor(this.clip[0]) + ")";
    
    for (var i=0;i<4;i++) {
      if (this.clip[i] != this.to[i]) break;
    }
    
    if (i < 4) {
      this.timer = setTimeout(function() {
        this.action();
      }.bind(this), this.interval);
    }
  }
});

