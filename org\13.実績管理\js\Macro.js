
var Macro = {
  handlers: {},
  apply: function(macros) {
    if (!macros) return;
    macro = macros.split("\|");
    for (var i = 0; i < macro.length; i++) {
      var name = macro[i].split("=")[0];
      var value = macro[i].split("=")[1];
      var values = value != "" ? value ? value.split(",") : [] : [""];
      if (!document.f[name]) continue;
      var elements = !document.f[name].tagName ? document.f[name] : [document.f[name]];
      for (var j = 0; j < elements.length; j++) {
        var element = elements[j];
        if (element.tagName != elements[0].tagName) continue;
        switch (element.tagName.toLowerCase()) {
          case 'input':
            if (element.type != elements[0].type) break;
            switch (element.type.toLowerCase()) {
              case 'text':
              case 'hidden':
                element.value = value;
                element.fireEvent('onchange');
                break;
              case 'checkbox':
                var checked = values.include(element.value);
                if (element.checked != checked) {
                  element.checked = checked;
                  element.fireEvent('onclick');
                  element.fireEvent('onchange');
                }
                break;
              case 'radio':
                if (element.checked = value ? values.include(element.value) : element.defaultChecked) {
                  element.fireEvent('onclick');
                  element.fireEvent('onchange');
                }
                break;
            }
            break;
          case 'textarea':
            element.value = value;
            element.fireEvent('onchange');
            break;
          case 'select':
            for (var k = 0; k < element.options.length; k++) {
              var option = element.options[k];
              option.selected = values.include(option.value);
            }
            element.fireEvent('onchange');
            break;
        }
      }
      if (this.handlers[name]) {
        this.handlers[name].each(function(handler) {
          handler();
        });
      }
    }
  },
  addHandler: function(name, handler) {
    if (!this.handlers[name]) {
      this.handlers[name] = [];
    }
    this.handlers[name].push(handler);
  }
}


