<%@ page contentType="text/html; charset=Windows-31J"%>
<%@page import="jp.co.vinculumjapan.mdware.common.util.HTMLUtil"%>
<%@page import="jp.co.vinculumjapan.mdware.common.bean.MdwUserBean"%>
<%@page import="jp.co.vinculumjapan.mdware.sso.common.FoundationUserBean"%>
<%
  MdwUserBean userBean = (MdwUserBean) session.getAttribute(FoundationUserBean.userBeanName);
  request.setCharacterEncoding("Windows-31J");
%>
<table width="450px" height="20" bgColor='#006000' border="0" cellspacing="0" cellpadding="0">
  <col width="30%">
  <col width="50%">
  <col width="20%">
  <tr>
    <td><span style="color:#FFFFFF;font-size: 20px;"><%=HTMLUtil.toLabel(userBean.getTenpoCd())%></span></td>
    <td><span style="color:#FFFFFF;font-size: 20px;"><%=HTMLUtil.toLabel(request.getParameter("PARAM") == null ? "" : request.getParameter("PARAM").toString())%></span></td>
    <td><span style="color:#FFFFFF;font-size: 20px;"><%=HTMLUtil.toLabel(userBean.getUserid())%></span></td>
  </tr>
</table>
