
var ActionMenu = Class.create();

ActionMenu.delay = 0;
ActionMenu.closeDelay = 1500;
ActionMenu.shadow = 2;

ActionMenu.registory = $H({});

ActionMenu.initialize = function() {
  Event.observe(document.body, "mouseup", function() {
    ActionMenu.handleEvent(document.body, "mouseup");
  } );
  Event.observe(window, "resize", function() {
    ActionMenu.handleEvent(window, "resize");
  } );
  Event.observe(document.body, "keydown", function() {
    ActionMenu.closeAll();
//    ActionMenu.observe(document.body, "keydown")
  } );
};

ActionMenu.create = function(name, label) {
  return ActionMenu.registory[name] = new ActionMenu(name, label);
};

ActionMenu.regist = function(menu) {
  ActionMenu.registory[menu.name] = menu;
};

ActionMenu.closeAll = function() {
  ActionMenu.registory.values().invoke("close");
};

ActionMenu.handleEvent = function(element, name) {
  ActionMenu.registory.values().each(function(menu) {
    menu.state["on" + name] && menu.state["on" + name](menu);
  });  
};

ActionMenu.State = {
  Closed: {
    name: "closed",
    onenter: function(menu) {
      menu.opened = false;
    }
  },
  Standby: {
    name: "standby",
    onenter: function(menu) {
      menu.timer = setTimeout(function() {
        menu.open(0);
      }, menu.delay);
    },
    onleave: function(menu) {
      clearTimeout(menu.timer);
      menu.timer = 0;
    },
    onmouseup: function(menu) {
      menu.setState(ActionMenu.State.Clicked);
    }
  },
  Opened: {
    name: "opened",
    onenter: function(menu) {
      menu.opened = true;
      
      $A(["mouseover", "mouseout"]).each(function(name) {
        Event.observe(menu.div, name, function() {
          menu.state["on" + name] && menu.state["on" + name](menu);
        }.bind(menu));
      });
      
    },
    onmouseup: function(menu) {
      menu.setState(ActionMenu.State.Released);
    },
    onkeydown: function(menu) {
      menu.close();
    }
  },
  Released: {
    name: "released",
    onenter: function(menu) {
      menu.released = true;
      menu.close(menu.closeDelay);
      menu.onclick = menu.srcElement.onclick;
      menu.srcElement.onclick = "";
//      status = "released";
    },
    onleave: function(menu) {
      menu.cancelClose();
      menu.srcElement.onclick = menu.onclick;
    },
    onmouseover: function(menu) {
      if (menu.within(event.x, event.y)) {
        menu.setState(ActionMenu.State.Focused);
      }
    },
    onmouseup: function(menu) {
      menu.close();
    },
    onresize: function(menu) {
      menu.close();
    }
  },
  Focused: {
    name: "focused",
    onenter: function(menu) {
//      status = "focused";
    },
    onleave: function(menu) {
    },
    onmouseout: function(menu) {
      if (!menu.within(event.x, event.y)) {
        menu.setState(ActionMenu.State.Released);
      }
    }
  },
  Selected: {
    name: "selected",
    onenter: function(menu) {
      if (document.readyState == "complete") {
        menu.close();
      } else {
        menu.close(menu.closeDelay);
      }
    }
  },
  Clicked: {
    name: "clicked",
    onenter: function(menu) {
      menu.close();
    }
  }
};

Event.observe(window, "load", ActionMenu.initialize);

ActionMenu.prototype = {
  initialize: function(name, label) {
    this.name = name;
    this.label = label;
    this.className = "ActionMenu";
    this.children = [];
    this.listeners = [];
    this.state = ActionMenu.State.Closed;
    this.x = 0;
    this.y = 0;
    this.delay = ActionMenu.delay;
    this.closeDelay = ActionMenu.closeDelay;
    this.shadow = ActionMenu.shadow;
    this.images = {
      left: "/images/left.gif",
      right: "/images/right.gif"
    };
    ActionMenu.regist(this);
  },
  
  add: function(child) {
    this.children.push(child);
    child.parent = this;
    return this;
  },
  
  addListener: function(listener) {
    this.listeners.push(listener);
  },
  
  display: function(table) {
    var row = this.row = $(table.insertRow());
    row.onmouseover = function(menu) {
      this.classNames().add("active");
      menu.activated = true;
      menu.direction = menu.parent.direction;
      menu.parent.closeChildren();
      menu.open();
    }.bind(row, this);
    row.onmouseout = function(menu) {
      this.classNames().remove("active");
      menu.activated = false;
    }.bind(row, this);
    row.onmouseup = function(menu) {
      this.onmouseout = null;
    }.bind(row, this);
    var cell = $(row.insertCell());
    cell.style.paddingLeft = 15;
    cell.style.paddingRight = 15;
    cell.appendChild(document.createTextNode(this.label));
  },
  
  open: function() {
    if (event && event.type == "mousedown") {
      if (!Event.isLeftClick(event)) return;
      this.srcElement = event.srcElement;
      this.x = event.x + document.body.scrollLeft;
      this.y = event.y + document.body.scrollTop;
      this.direction = (this.x > (document.body.clientWidth / 2)) | (this.y > (document.body.clientHeight / 2)) << 1;
    }    
    if (this.delay && this.state != ActionMenu.State.Standby) {
      this.setState(ActionMenu.State.Standby);
      return;
    }
    
    ActionMenu.closeAll();
    
    var div = this.div = document.createElement("div");
    div.style.position = "absolute";
    div.style.zIndex = 100 + this.parent ? 1 : 0;
    div.style.left = 0;
    div.style.right = 0;
    div.style.height = 0;
    div.style.width = 0;
    div.style.visibility = "hidden";

    if (this.name) {
      div.id = this.name;
    }
    div.className = this.className;
    
//    document.body.insertBefore(div, document.body.firstChild);
    this.srcElement.parentElement.appendChild(div);
    
    var table = this.table = document.createElement("table");
    table.style.position = "absolute";
    table.style.zIndex = div.style.zIndex;
    table.cellPadding = 10;
    table.cellSpacing = 1;
    
    div.appendChild(table);
    
    this.children.each(function(item) {
      item.display(table);
    });
   	
	div.style.width = table.offsetWidth + this.shadow;
	div.style.height = table.offsetHeight + this.shadow;
    
    if (this.shadow) {
      var shadow = document.createElement("div");
      shadow.className = "shadow";
      shadow.style.position = "absolute";
      shadow.style.zIndex = table.style.zIndex - 1;
      shadow.style.left = this.shadow;
      shadow.style.top = this.shadow;
      shadow.style.width = table.offsetWidth;
      shadow.style.height = table.offsetHeight;
      div.appendChild(shadow);
	}
	
    var shim = document.createElement("iframe");
    shim.style.position = "absolute";
    shim.style.zIndex = table.style.zIndex - 2;
    shim.style.width = table.offsetWidth + this.shadow;
    shim.style.height = table.offsetHeight + this.shadow;
    shim.style.borderStyle = "none";
    shim.style.filter = "Alpha(opacity=0)";
    shim.src = "javascript:void(0)";
    div.appendChild(shim);
    
    if (this.parent) {
      var activeItem = this.parent.activeItem();
      this.x = this.parent.div.offsetLeft;
      if (this.direction & 0x01) {
        this.x -= table.clientTop * 2
      } else {
        this.x += this.parent.div.clientWidth;
      }
      this.y = this.parent.div.offsetTop - this.parent.div.clientTop - (table.clientTop * 2);
      if (this.direction & 0x02) {
        this.y += this.parent.div.clientHeight;
      } else {
        this.y += activeItem.row.offsetTop;
      }
    }
        
    this.x -= (this.direction & 0x01) ? table.clientWidth : 0;
    this.y -= (this.direction & 0x02) ? table.clientHeight : 0;
    
    this.left = div.style.left = this.x;
    this.top = div.style.top = this.y;
    this.width = div.style.width = div.offsetWidth + this.shadow + 1;
    this.height = div.style.height = div.offsetHeight + this.shadow + 1;
    
    div.style.visibility = "visible";
    
   	this.setState(ActionMenu.State.Opened);
  },
  
  close: function(delay) {
    if (this.state == ActionMenu.State.Closed) return;
    if (delay) {
      this.timer = setTimeout(function() {
        this.close();
      }.bind(this), delay);
      return;
    }
    this.closeChildren();
    try {
      this.div.parentElement.removeChild(this.div);
      this.div = null;
    } catch (e) {}
   	this.setState(ActionMenu.State.Closed);
  },
  
  cancelClose: function() {
    this.timer && clearTimeout(this.timer);
    this.timer = 0;
  },
  
  closeChildren: function() {
   	this.children.each(function(child) {
   	  child.close();
   	});
  },
    
  setState: function(state) {
    this.state.onleave && this.state.onleave(this);
    this.state = state;
    state.onenter && state.onenter(this);
    this.fireEvent(state.name);
  },
  
  fireEvent: function(name) {
    var menu = this;
    menu.listeners.each(function(listener) {
      listener[name] && listener[name]({
        menu: menu,
        item: menu.activeItem()
      });
    });
  },
  
  within: function(x, y) {
    if (x <= this.div.offsetLeft + 1) return false;
    if (x >= this.div.offsetLeft + this.div.offsetWidth) return false;
    if (y <= this.div.offsetTop + 1) return false;
    if (y >= this.div.offsetTop + this.div.offsetHeight) return false;
    return true;
  },  
    
  select: function(item) {
    item && (item.activated = true);
    this.setState(ActionMenu.State.Selected);
    if (this.parent) {
      this.parent.select();
    }
  },
  
  activeItem: function() {
    return this.children.find(function(item) {
      return item.activated;
    });
  },
  
  find: function(name) {
    for (var i=0;i<this.children.length;i++) {
      var item = this.children[i];
      if (item.find) {
        var found = item.find(name);
        if (found) return found;
      } else if (item.name == name) {
        return item;
      }
    }
    return null;
  }
  
};

var ActionMenuItem = Class.create();
ActionMenuItem.prototype = {

  initialize: function(name, label) {
    this.name = name;
    this.label = label;
  },
  
  display: function(table) {
    var row = this.row = $(table.insertRow());
    row.onmouseover = function(item) {
      this.classNames().add("active");
      item.activated = true;
      item.parent.closeChildren();
    }.bind(row, this);
    row.onmouseout = function(item) {
      this.classNames().remove("active");
      item.activated = false;
    }.bind(row, this);
    row.onmouseup = function(item) {
//      this.onmouseout = null;
      item.select();
    }.bind(row, this);
    var cell = $(row.insertCell());
    cell.noWrap = true;
    cell.appendChild(document.createTextNode(this.label));
  },
  
  close: function() {
  },
  
  select: function() {
    var menu = this.parent;
    menu.select(this);
  }
  
};

var ActionMenuSeparator = Class.create();
ActionMenuSeparator.prototype = {

  initialize: function() {
  },
  
  display: function(table) {
    var menu = this.parent;
    var row = $(table.insertRow());
    var cell = $(row.insertCell());
    cell.style.height = 1;
    cell.className = "separator";
  },
  
  close: function() {
  }
  
};

