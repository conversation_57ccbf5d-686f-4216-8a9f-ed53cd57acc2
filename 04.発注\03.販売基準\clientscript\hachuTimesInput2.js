$(function () {
	$('input[name="upload"]').disabled(true);
	$('input[name="fileupload.data"]').bind("change", function () {
		var upload_disable = $("input[name='upload_disable.value']").val();
		if (upload_disable == "true") {
			$("#message").text("CSVアップロード機能は店舗権限では使用できません。");
			$("#message").css("color", "#FF0000");
			$('input[name="upload"]').disabled(true);
			return;
		}
		$("#message").text("");
		$("#message").css("background-color", "");
		var file;
		if (this.files !== undefined) {
			file = this.files[0].name;
		} else {
			file = this.value;
		}
		if (file != null) {
			var regx1 = new RegExp(".+\.csv$");
			var regx2 = new RegExp(".+\.CSV$");
			if (!regx1.test(file) && !regx2.test(file)) {
				$("#message").text("拡張子はCSVでアップロードしてください。");
				$("#message").css("color", "#FF0000");
				$('input[name="upload"]').disabled(true);
			} else {
				$("#message").text("");
				$('input[name="upload"]').disabled(false);
			}
		}
	});
});
$(window).load(function () {
	updateDialog();
});
function updateDialog() {
	var action = $("input[name='upload_flg.value']").val();
	if (action == "upload_syohin" || action == "upload_bunrui") {
		newwindow(action);
		// popup 再表示防止のため、formをリセット
		$("input[name='upload_flg.value']").val("");
	}
}
function  newwindow(action) {
	// 2020/04/20 AN.HT マルチブラウザ対応(S)
	if (isChrome() || isEdgeChromium()) {
		nwin = window.open(
					"./HachuTimesCsvUploadResult.do?action_code="+action,   //移動先
					this,  //ダイアログに渡すパラメータ（この例では、自分自身のwindowオブジェクト）
					"width=768,height=600", null);
	} else {
		nwin = window.showModalDialog(
			"./HachuTimesCsvUploadResult.do?action_code="+action,   //移動先
			this,  //ダイアログに渡すパラメータ（この例では、自分自身のwindowオブジェクト）
			"dialogWidth=768px; dialogHeight=600px;"
		);
	}
        // 2020/04/20 AN.HT マルチブラウザ対応(E)
}
jQuery.fn.disabled = function (flag) {
	if (undefined == flag) {
		return undefined != jQuery(this).attr("disabled");
	}
	return this.each(function () {
		if (flag) {
			if (undefined == jQuery(this).attr("disabled")) {
				jQuery(this).attr("disabled", "disabled");
			}
		} else {
			if (undefined != jQuery(this).attr("disabled")) {
				jQuery(this).removeAttr("disabled");
			}
		}
	});
}