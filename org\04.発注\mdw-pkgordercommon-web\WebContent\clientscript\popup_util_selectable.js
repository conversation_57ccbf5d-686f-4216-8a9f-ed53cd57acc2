﻿/*
 * ポップアップ用スクリプト
 *
 * ※ 基本的には、ordercommon ⇔ autoorder ⇔ dailyorder ⇔ orderzaiko でコピペ
 *
 * ※ ！！！ 各プロジェクトでシステム区分を書換えること ！！！
 *     （絞り込み不要の場合は指定しない（SYSTEM_KB = '';））
 */

/*******************************************************************************
 *
 * ページ変数の宣言
 *
 *******************************************************************************/

/* ポップアップ画面へのシステム区分 */
var SYSTEM_KB = '';

// ポップアップ画面のID(popup_manager.xmlで指定)
var popUpIdNames = {
	'bunrui1_button'        : 'AUT90902',
	'bunrui2_button'        : 'AUT90903',
	'bunrui3_button'        : 'AUT90904',
	'bunrui4_button'        : 'AUT90905',
	'bunrui5_button'        : 'AUT90906',
	'syohin_button'         : 'AUT90907',
	'area_button'           : 'KYT02007',
	'torihikisaki_button'   : 'KYT02008',
	'tenpo_button'          : 'AUT90901',
	'copy_tenpo_button'     : 'AUT90901',
	'ten_group_button'      : 'AUT90914',
	'theme_button'          : 'AUT90911',
	'gondola_button'        : 'AUT90913',
	'daily_gondola_button'  : 'AUT90913',
	'weekly_gondola_button' : 'KYT02014',
	'syohin_group_button'   : 'AUT90914'
};

// ポップアップ画面への必須パラメータの物理名
var popUpRequiredParamPhysicalNames = {
	'bunrui1_button'        : new Array(SYSTEM_KB),
	'bunrui2_button'        : new Array(SYSTEM_KB, 'bunrui1_cd.value'),
	'bunrui3_button'        : new Array(SYSTEM_KB, 'bunrui1_cd.value', 'bunrui2_cd.value'),
	'bunrui4_button'        : new Array(SYSTEM_KB, 'bunrui1_cd.value', 'bunrui2_cd.value', 'bunrui3_cd.value'),
	'bunrui5_button'        : new Array(SYSTEM_KB, 'bunrui1_cd.value', 'bunrui2_cd.value', 'bunrui3_cd.value', 'bunrui4_cd.value'),
	'syohin_button'         : new Array('bunrui1_cd.value'),
	'area_button'           : new Array(),
	'torihikisaki_button'   : new Array(),
	'tenpo_button'          : new Array(),
	'copy_tenpo_button'     : new Array(),
	'ten_group_button'      : new Array(),
	'theme_button'          : new Array('tenpo_cd.value'),
	'gondola_button'        : new Array('tenpo_cd.value', 'bunrui3_cd.value'),
	'daily_gondola_button'  : new Array('tenpo_cd.value', 'bunrui3_cd.value'),
	'weekly_gondola_button' : new Array('tenpo_cd.value', 'bunrui3_cd.value'),
	'syohin_group_button'   : new Array('bunrui3_cd.value')
};

// ポップアップ画面への任意パラメータの物理名
var popUpOptionalParamPhysicalNames = {
	'bunrui1_button'        : new Array(),
	'bunrui2_button'        : new Array(),
	'bunrui3_button'        : new Array(),
	'bunrui4_button'        : new Array(),
	'bunrui5_button'        : new Array(),
	'syohin_button'         : new Array('bunrui2_cd.value', 'bunrui3_cd.value'),
	'area_button'           : new Array(),
	'torihikisaki_button'   : new Array(),
	'tenpo_button'          : new Array(),
	'copy_tenpo_button'     : new Array(),
	'ten_group_button'      : new Array(),
	'theme_button'          : new Array('bunrui1_cd.value'),
	'gondola_button'        : new Array(),
	'daily_gondola_button'  : new Array(),
	'weekly_gondola_button' : new Array(),
	'syohin_group_button'   : new Array()
};

// ポップアップ画面へのパラメータの論理名
var popUpParamLogicalNames = {
	'tenpo_cd.value'        : '店舗',
	'bunrui1_cd.value'      : '分類１',
	'bunrui2_cd.value'      : '分類２',
	'bunrui3_cd.value'      : '分類３',
	'bunrui4_cd.value'      : '分類４',
	'bunrui5_cd.value'      : '分類５'
};

/* 変換マップ */
var convertedNames = {
    'daily_gondola'         : 'gondola',
    'weekly_gondola'        : 'gondola'
};

/*******************************************************************************
*
* 各種選択ボタン押下（ポップアップ用関数）
*
*******************************************************************************/
function popup_select(targetNames) {
	var HACHU_KB = '';
    // クリックしたボタン名
    var buttonName = window.event.srcElement.name;

    // 必須パラメータの入力チェックと値の退避
    var params = new Array();
    var reqNames = popUpRequiredParamPhysicalNames[buttonName];
    if (reqNames != null) {
        for (var i = 0; i < reqNames.length; i++) {
            // 上でパラメータの記述をしている場合はその数ループ

            var paramName = reqNames[i];
            if (paramName == SYSTEM_KB) {
                params.push(HACHU_KB);
            } else {

                // システム区分の指定でなければ値を確認。OKなら退避する。
                var paramElement = document.getElementsByName(paramName)[0];
                var paramValue = paramElement == null ? "" : paramElement.value;
                if (paramValue == '') {
                    document.getElementsByName(paramName.replace(/_cd\.value/, '_na.value'))[0].value = "";
                    alert(popUpParamLogicalNames[paramName] + 'を先に入力して下さい。');
                    paramElement.select();
                    paramElement.focus();
                    return false;
                }
                params.push(paramElement.value);
            }
        }
    }

    // 任意パラメータの値の退避
    var optNames = popUpOptionalParamPhysicalNames[buttonName];
    if (optNames != null) {
        for (var i = 0; i < optNames.length; i++) {
            // 上でパラメータの記述をしている場合はその数ループ

            var paramName = optNames[i];
            if (paramName == SYSTEM_KB) {
                params.push(HACHU_KB);
            } else {

                // システム区分の指定でなければ退避する。
                var paramElement = document.getElementsByName(paramName)[0];
                var paramValue = paramElement == null ? "" : paramElement.value;
                params.push(paramElement.value);
            }
        }
    }

    //--------------------------------------------------------------------------
    //
    // 戻り値設定用の要素を退避（cd・naの要素を渡す）
    //
    //--------------------------------------------------------------------------
    var prefix = buttonName.replace(/_button$/, ''); // xxxx_button → xxxx
    var convertedName = convertedNames[prefix];
    if (convertedName != null) {prefix = convertedName;}

    var cd = document.getElementsByName(prefix + '_cd.value')[0];
    var nb = document.getElementsByName(prefix + '_nb.value')[0];
    var id = document.getElementsByName(prefix + '_id.value')[0];
    var ret;

    if (cd != null) {
        ret = new Array('getElementsByName("' + prefix + '_cd.value")[0]', 'getElementsByName("' + prefix + '_na.value")[0]');
    } else if (nb != null) {
        ret = new Array('getElementsByName("' + prefix + '_nb.value")[0]', 'getElementsByName("' + prefix + '_na.value")[0]');
    } else if (id != null) {
        ret = new Array('getElementsByName("' + prefix + '_id.value")[0]', 'getElementsByName("' + prefix + '_na.value")[0]');
    }

    // テーマ検索時は1/3番目の値を戻すよう要素を設定
    if (prefix == 'theme') {
        ret = new Array('getElementsByName("' + prefix + '_cd.value")[0]', 'getElementsByName("' + prefix + '_na.value")[0]', 'getElementsByName("' + prefix + '_na.value")[0]');
    }

    //--------------------------------------------------------------------------
    //
    // 子画面呼出（共通func）
    //
    //--------------------------------------------------------------------------
    open_common_popup(popUpIdNames[buttonName], ret, params);
}

/*******************************************************************************
 *
 * 各種クリアボタン押下
 *
 *******************************************************************************/
function popup_clear() {
    var buttonName = window.event.srcElement.name;
    var prefix = buttonName.replace(/_clear$/, '');
    var cd = document.getElementById(prefix + '_cd.value');
    var na = document.getElementById(prefix + '_na.value');
    var nb = document.getElementById(prefix + '_nb.value');
    var id = document.getElementById(prefix + '_id.value');
    try {
        if (cd != null) { cd.value = ''; }
        if (na != null) { na.value = ''; }
        if (nb != null) { nb.value = ''; }
        if (id != null) { id.value = ''; }
    } catch(e) {
    }
}

/*******************************************************************************
 *
 * カレンダーボタン押下
 *
 *******************************************************************************/
function popup_calendar(fTag, ymdTag) {
	var str = "<%=StcLibProperty.dirCommon%>"+"/"+"calendar.jsp?calendarXPos=0&calendarYPos=100&YEAR=" + ymdTag.value.substring(0,4) + "&MONTH=" + ymdTag.value.substring(4,6) + "&calendarForm=" + fTag.name + "&dateTag=" + ymdTag.name;
	newwin = window.open(str,'new2',"width=255, height=200");
}
