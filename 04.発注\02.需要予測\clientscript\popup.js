﻿/***********************************************************
 *
 * 日配発注　ポップアップ用スクリプト
 *
 ***********************************************************/

/*
 * ページ変数の宣言
 */

// ポップアップ画面へのシステム区分
var SYSTEM_KB = '';

// ポップアップ画面のID(popup_manager.xmlで指定)
var popUpIdNames = {
	'bunrui1_button'        : 'AUT90902',
	'bunrui2_button'        : 'AUT90903',
	'bunrui3_button'        : 'AUT90904',
	'bunrui4_button'        : 'AUT90905',
	'bunrui5_button'        : 'AUT90906',
	'syohin_button'         : 'AUT90907',
	'area_button'           : 'KYT02007',
	'shiiresaki_button'     : 'KYT02008',
	'tenpo_button'          : 'AUT90901',
	'copy_tenpo_button'     : 'AUT90901',
	'ten_group_button'      : 'AUT90914',
	'theme_button'          : 'AUT90911',
	'daily_gondola_button'  : 'AUT90913',
	'weekly_gondola_button' : 'KYT02014',
	'syohin_group_button'   : 'AUT90915',
	'okurikomi_button'      : 'AUT02053'
};

// ポップアップ画面への必須パラメータの物理名
var popUpRequiredParamPhysicalNames = {
	'bunrui1_button'        : new Array(SYSTEM_KB),
	'bunrui2_button'        : new Array(SYSTEM_KB, 'bunrui1_cd.value'),
	'bunrui3_button'        : new Array(SYSTEM_KB, 'bunrui1_cd.value', 'bunrui2_cd.value'),
	'bunrui4_button'        : new Array(SYSTEM_KB, 'bunrui1_cd.value', 'bunrui2_cd.value', 'bunrui3_cd.value'),
	'bunrui5_button'        : new Array(SYSTEM_KB, 'bunrui1_cd.value', 'bunrui2_cd.value', 'bunrui3_cd.value', 'bunrui4_cd.value'),
	'syohin_button'         : new Array('bunrui1_cd.value'),
	'area_button'           : new Array(),
	'shiiresaki_button'     : new Array(),
	'tenpo_button'          : new Array(),
	'copy_tenpo_button'     : new Array(),
	'ten_group_button'      : new Array(),
	'theme_button'          : new Array('tenpo_cd.value'),
	'daily_gondola_button'  : new Array('tenpo_cd.value', 'bunrui3_cd.value'),
	'weekly_gondola_button' : new Array('tenpo_cd.value', 'bunrui3_cd.value'),
	'syohin_group_button'   : new Array('bunrui3_cd.value'),
	'okurikomi_button'   : new Array()
};

// ポップアップ画面への任意パラメータの物理名
var popUpOptionalParamPhysicalNames = {
	'bunrui1_button'        : new Array(),
	'bunrui2_button'        : new Array(),
	'bunrui3_button'        : new Array(),
	'bunrui4_button'        : new Array(),
	'bunrui5_button'        : new Array(),
	'syohin_button'         : new Array('bunrui2_cd.value', 'bunrui3_cd.value'),
	'area_button'           : new Array(),
	'shiiresaki_button'     : new Array(),
	'tenpo_button'          : new Array(),
	'copy_tenpo_button'     : new Array(),
	'ten_group_button'      : new Array(),
	'theme_button'          : new Array('bunrui1_cd.value'),
	'daily_gondola_button'  : new Array(),
	'weekly_gondola_button' : new Array(),
	'syohin_group_button'   : new Array(),
	'okurikomi_button'   : new Array()
};

// ポップアップ画面へのパラメータの論理名
var popUpParamLogicalNames = {
	'tenpo_cd.value' : '店舗',
	'bunrui1_cd.value' : '分類１',
	'bunrui2_cd.value' : '分類２',
	'bunrui3_cd.value' : '分類３',
	'bunrui4_cd.value' : '分類４',
	'bunrui5_cd.value' : '分類５'
};

/***********************************************************
 * 各種選択ボタン押下（ポップアップ用関数）
 ***********************************************************/
function popup_select(targetNames) {
	// クリックしたボタン名
	var buttonName = window.event.srcElement.name;

	// 必須パラメータの入力チェックと値の退避
	var params = new Array();
	var reqNames = popUpRequiredParamPhysicalNames[buttonName];
	if (reqNames != null) {
		for (var i = 0; i < reqNames.length; i++) {
			// 上でパラメータの記述をしている場合はその数ループ

			var paramName = reqNames[i];
			if (paramName == SYSTEM_KB) {
				params.push(paramName);
			} else {

				// システム区分の指定でなければ値を確認。OKなら退避する。
				var paramElement = document.getElementsByName(paramName)[0];
				var paramValue = paramElement == null ? "" : paramElement.value;
				if (paramValue == '') {
					document.getElementsByName(paramName.replace(/_cd\.value/, '_na.value'))[0].value = "";
					alert(popUpParamLogicalNames[paramName] + 'を先に入力して下さい。');
					paramElement.select();
					paramElement.focus();
					return false;
				}
				params.push(paramElement.value);
			}
		}
	}

	// 任意パラメータの値の退避
	var optNames = popUpOptionalParamPhysicalNames[buttonName];
	if (optNames != null) {
		for (var i = 0; i < optNames.length; i++) {
			// 上でパラメータの記述をしている場合はその数ループ

			var paramName = optNames[i];
			if (paramName == SYSTEM_KB) {
				params.push(paramName);
			} else {

				// システム区分の指定でなければ退避する。
				var paramElement = document.getElementsByName(paramName)[0];
				var paramValue = paramElement == null ? "" : paramElement.value;
				params.push(paramElement.value);
			}
		}
	}

	// 「・・・_button」の「・・・」を取得してcd・naの要素を渡す
	var prefix = buttonName.replace(/_button$/, '');
	if (prefix == 'weekly_gondola') prefix = 'gondola';
	if (prefix == 'daily_gondola') prefix = 'gondola';
	if (targetNames != null) {
		var ret = new Array();
		for (i = 0; i < targetNames.length; i++) {
			ret.push('getElementsByName("' + targetNames[i] + '")[0]');
		}
	} else {
		var ret = null;

		// テーマ検索時は1/3番目の値を戻すよう要素を設定
		if (prefix != 'theme') {
			ret = new Array('getElementsByName("' + prefix + '_cd.value")[0]', 'getElementsByName("' + prefix + '_na.value")[0]');
		} else {
			ret = new Array('getElementsByName("' + prefix + '_cd.value")[0]', 'getElementsByName("' + prefix + '_na.value")[0]', 'getElementsByName("' + prefix + '_na.value")[0]');
		}
		if (prefix =='okurikomi') {
			ret = new Array('getElementsByName("' + prefix + '_no.value")[0]', 'getElementsByName("' + prefix + '_na.value")[0]');
		}
	}
	open_common_popup(popUpIdNames[buttonName], ret, params);
}

/***********************************************************
 * 各種クリアボタン押下
 ***********************************************************/
function popup_clear() {

    var buttonName = window.event.srcElement.name;
    var prefix = buttonName.replace(/_clear$/, '');
    var cd = elements_getElement(prefix + '_cd.value');
    var na = elements_getElement(prefix + '_na.value');
    var nb = elements_getElement(prefix + '_nb.value');
    var id = elements_getElement(prefix + '_id.value');
    var no = elements_getElement(prefix + '_no.value');
    try {
        if (cd != null) { cd.value = ''; }
        if (na != null) { na.value = ''; }
        if (nb != null) { nb.value = ''; }
        if (id != null) { id.value = ''; }
        if (no != null) { no.value = ''; }
    } catch(e) {
    }
}

/***********************************************************
 * カレンダーボタン押下
 ***********************************************************/
function popup_calendar(fTag, ymdTag) {
	var str = "<%=StcLibProperty.dirCommon%>"+"/"+"calendar.jsp?calendarXPos=0&calendarYPos=100&YEAR=" + ymdTag.value.substring(0,4) + "&MONTH=" + ymdTag.value.substring(4,6) + "&calendarForm=" + fTag.name + "&dateTag=" + ymdTag.name;
	newwin = window.open(str,'new2',"width=255, height=200");
}
