﻿/*
 * ファイル名	: rbsite_date.js
 * 説明			: Java Script 外部ファイル（日付関連 Ver2.1以降に使用）
 * 著作権		: Copyright (c) 2002-2003
 * 会社名		: Vinculum Japan Corporation
 *
 *
 * 【グローバル変数一覧と概要】
 *
 *
 * 【関数一覧と概要】
 *
 *		・日付処理関連
 *
 *			dateAdd(dateStr, inc)					日付の加減算を行います。
 *			isValidDate(dateStr)					日付の妥当性をチェックします。
 *			formatYYYY_MM_DDFromString(dateStr)		8 桁の文字列（数字）の場合のみ '/' 区切りの日付文字列に変換します
 *			dateFormalizer(target)					指定されたtargetの日付不正入力＆妥当性チェックを行います。
 *													※isValidDate(dateStr)の包み版です。
 *
 *		・カレンダ呼出関連
 *
 *			callCalendar( fTag, ymdTag )			STCLIB で提供されているカレンダ画面を呼び出します。
 *
 *
 * <AUTHOR>
 * @version 1.3 (2005.09.12) mKusumoto 'yyyy/m/m'形式の日付入力に対応(formatYYYY_MM_DDFromString)
 * @version 1.2 (2003.06.10) (MJC)deguchi ist_date.jsよりrbsite用にカスタマイズ
 * @version 1.1 (2003.03.10) カレンダーポップアップの高速化
 * @version 1.0 (2002.12.25) 初版作成
 */


/*
 * 日付の加減算を行います。
 *
 * dateStr	日付の文字列（'/'区切り）
 * inc		加算する日数（減算する場合は負数で指定します）
 * return	加減算された日付の文字列（'/'区切り）
 */
function dateAdd(dateStr, inc) {

	var date = new Date(dateStr);

	date.setDate( date.getDate() + inc );

	var yy = date.getYear();
	var mm = date.getMonth() + 1;
	var dd = date.getDate();
	if (yy < 2000) { yy += 1900; }
	if (mm < 10) { mm = '0' + mm; }
	if (dd < 10) { dd = '0' + dd; }
	if (yy > 9999) {
		yy = 9999;
		mm = 12;
		dd = 31;
	}
	var rtnDateStr = String(yy) + '/' + String(mm) + '/' + String(dd);
	return rtnDateStr;
}


/*
 * 日付の妥当性をチェックします。
 *
 * dateStr	日付の文字列
 * return	日付として妥当ならば true
 */
function isValidDate(dateStr) {
	var date = new Date(dateStr);

	if (date == NaN) {
		// 日付オブジェクトに変換できなければ不当
		return false;
	}

	// 日付オブジェクトに変換できても…
	var yy = date.getYear();
	if (yy < 2000) { yy += 1900; }
	var mm = date.getMonth() + 1;
	var dd = date.getDate();
	
	// Date は 2002/12/40 を 2003/1/9 と判断し、エラーとして認識しません。
	// ですので、Date 型を String に戻し、引数と一致するかを判断します。
	var dateArray = dateStr.split("/", 3);
	
	if (Number(dateArray[0]) == yy
	 && Number(dateArray[1]) == mm
	 && Number(dateArray[2]) == dd ) {
		return true;
	} else {
		return false;
	}
}


/*
 * 8 桁の文字列（数字）の場合のみ '/' 区切りの日付文字列に変換します
 * 8 桁の文字列でない場合は何もせず、引数で渡された文字列をそのまま返却します。
 *
 * dateStr	変換する文字列
 * return	変換された文字列
 */
function formatYYYY_MM_DDFromString(dateStr) {
	var rtnDateStr = null;

//2005.09.12 mKusumoto add start --末尾の'/'対応
	if (dateStr.substring(dateStr.length -1, dateStr.length) == '/') {
		dateStr = dateStr.substring(0,dateStr.length -1);
		if (dateStr.substring(dateStr.length -1, dateStr.length) == '/') {
			dateStr = dateStr.substring(0,dateStr.length -1);
		}	
	}	
//2005.09.12 mKusumoto add end
	if (dateStr.length == 8 && isNaN(dateStr) == false && dateStr.indexOf('/') == -1 ) {
		// '/' なしの 8 桁の文字列の場合
		rtnDateStr = dateStr.substring(0, 4);
		rtnDateStr = rtnDateStr + '/' + dateStr.substring(4, 6);
		rtnDateStr = rtnDateStr + '/' + dateStr.substring(6, 8);
//2005.09.12 mKusumoto add start --'yyyy/m/m,yyyy/m/dd,yyyy/mm/d'形式の対応
	} else if (dateStr.length == 8 && dateStr.substring(4, 5) == '/' && dateStr.substring(6, 7) == '/') {
		rtnDateStr = dateStr.substring(0, 4);
		rtnDateStr = rtnDateStr + '/0' + dateStr.substring(5, 6);
		rtnDateStr = rtnDateStr + '/0' + dateStr.substring(7, 8);
	} else if (dateStr.length == 9 && dateStr.substring(4, 5) == '/' && dateStr.substring(7, 8) == '/') {
		rtnDateStr = dateStr.substring(0, 4);
		rtnDateStr = rtnDateStr + '/' + dateStr.substring(5, 7);
		rtnDateStr = rtnDateStr + '/0' + dateStr.substring(8, 9);
	} else if (dateStr.length == 9 && dateStr.substring(4, 5) == '/' && dateStr.substring(6, 7) == '/') {
		rtnDateStr = dateStr.substring(0, 4);
		rtnDateStr = rtnDateStr + '/0' + dateStr.substring(5, 6);
		rtnDateStr = rtnDateStr + '/' + dateStr.substring(7, 9);
//2005.09.12 mKusumoto add end
	} else {
		rtnDateStr = dateStr;
	}
	return rtnDateStr;
}

/*
 * 指定されたtargetの日付不正入力＆妥当性チェックを行います。
 * 不正入力が行われていた場合、指定されたtargetにフォーカスを戻します。
 *
 * target	日付チェックを行う項目要素
 */
function dateFormalizer(target) {
	var dt = formatYYYY_MM_DDFromString( target.value );
	dt = dt.replace(/[ 　]+/, "k");//スペースは適当な一文字に変換して、エラーが出るようにする。
	if (dt == '') {
		return;
	}
	else if (isValidDate(dt) == false || target.value.length > 8) {//エラーだったら、
		target.focus();
		target.select();
		alert('正しい日付を入力してください。');
		target.value = '';//値を消去(2003/06/28deguchi ADD)
		target.select();//セレクトしなおし。
	}
}

/*
 *@@@okajima add start
 */
/*
 * 10 桁の文字列（YYYY/MM/DD）の場合のみ '/' 区切りなしの日付文字列(YYYYMMDD)に変換します
 * 10 桁の文字列でない場合は何もせず、引数で渡された文字列をそのまま返却します。
 *
 * target	日付チェックを行う項目要素
 */
function formatYYYYMMDDFromString(dateStr) {
	var rtnDateStr = null;
	var sndDateStr = dateStr.value;
	if ( sndDateStr == '' ) {
		return;
	} else if (sndDateStr.length == 10 && sndDateStr.substring(4, 5) == '/' && sndDateStr.substring(7, 8) == '/') {
		// '/' ありの 10 桁の文字列の場合
		rtnDateStr = sndDateStr.substring(0, 4);
		rtnDateStr = rtnDateStr + sndDateStr.substring(5, 7);
		rtnDateStr = rtnDateStr + sndDateStr.substring(8, 10);
	} else {
		rtnDateStr = sndDateStr;
	}
   	dateStr.value = rtnDateStr;
	dateStr.select();
}

/*
 * 指定されたtargetの日付不正入力＆妥当性チェックを行います。
 * 不正入力が行われていた場合、指定されたtargetにフォーカスを戻します。
 *
 * target	日付チェックを行う項目要素
 */
function dateFormalizer2(target) {
	var dt = '';
	if (target.value.length == 10 && target.value.substring(4, 5) == '/' && target.value.substring(7, 8) == '/') {
		dt = target.value;
	}
	dt = dt.replace(/[ 　]+/, "k");//スペースは適当な一文字に変換して、エラーが出るようにする。
	if (dt == '') {
		return;
	}
	else if (isValidDate(dt) == false || target.value.length > 10) {//エラーだったら、
		target.focus();
		target.select();
		alert('正しい日付を入力してください。');
		target.value = '';//値を消去(2003/06/28deguchi ADD)
		target.select();//セレクトしなおし。
	}
	else {
		target.value = dt;
	}
}
/*
 *@@@okajima add end
 */

/**************************
 * カレンダー画面を呼び出す
 **************************/
function callCalendar( fTag, ymdTag )
{
	// 2018.08.02 HA.NTT Upd(S) 発注管理モジュール統合化対応
	// var str = "calendar.jsp?calendarXPos=0&calendarYPos=100&YEAR=" + ymdTag.value.substring(0,4) + "&MONTH=" + ymdTag.value.substring(4,6) + "&calendarForm=" + fTag.name + "&dateTag=" + ymdTag.name;
	var str = "<%=StcLibProperty.dirCommon%>"+"/"+"calendar.jsp?calendarXPos=0&calendarYPos=100&YEAR=" + ymdTag.value.substring(0,4) + "&MONTH=" + ymdTag.value.substring(4,6) + "&calendarForm=" + fTag.name + "&dateTag=" + ymdTag.name;
	// 2018.08.02 HA.NTT Upd(E) 発注管理モジュール統合化対応
	newwin = window.open(str,'new2',"width=255, height=230");
}
