DIV#detail_head_title {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: 0px; MARGIN: 0px; WIDTH: 183px; PADDING-TOP: 0px
}
DIV#detail_head_base {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: 0px; MARGIN: 0px; OVERFLOW: hidden; WIDTH: 780px; PADDING-TOP: 0px
}
DIV#detail_head {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: 0px; MARGIN: 0px; OVERFLOW: hidden; WIDTH: 5230px; PADDING-TOP: 0px
}
DIV#detail_main_title {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: 0px; MARGIN: 0px; OVERFLOW: hidden; PADDING-TOP: 0px; HEIGHT: 310px
}
DIV#detail_main_base {
	PADDING-RIGHT: 0px; OVERFLOW-Y: scroll; PADDING-LEFT: 0px; OVERFLOW-X: hidden; PADDING-BOTTOM: 0px; MARGIN: 0px; WIDTH: 797px; PADDING-TOP: 0px; HEIGHT: 310px
}
DIV#detail_total_title {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: 0px; MARGIN: 0px; PADDING-TOP: 0px
}
DIV#detail_total_base {
	PADDING-RIGHT: 0px; OVERFLOW-Y: hidden; PADDING-LEFT: 0px; OVERFLOW-X: scroll; PADDING-BOTTOM: 0px; MARGIN: 0px; WIDTH: 780px; PADDING-TOP: 0px
}
TABLE.table_detail_head {
	BORDER-RIGHT: 0px; BORDER-TOP: 0px; BORDER-LEFT: 0px; BORDER-BOTTOM: 0px; BORDER-COLLAPSE: collapse; empty-cells: show; border-spacing: 0px
}
TABLE.table_detail_head TH.headcol {
	BORDER-RIGHT: #aaaaaa 1px solid; PADDING-RIGHT: 0px; BORDER-TOP: #aaaaaa 1px solid; PADDING-LEFT: 0px; FONT-WEIGHT: bold; PADDING-BOTTOM: 0px; BORDER-LEFT: #aaaaaa 1px solid; WIDTH: 30px; PADDING-TOP: 0px; BORDER-BOTTOM: #aaaaaa 1px solid; HEIGHT: 30px; BACKGROUND-COLOR: #b0c4de
}
TABLE.table_detail_head TH.cols {
	BORDER-RIGHT: #aaaaaa 1px solid; PADDING-RIGHT: 1px; BORDER-TOP: #aaaaaa 1px solid; FONT-WEIGHT: bold; BORDER-LEFT: #aaaaaa 0px solid; WIDTH: 150px; BORDER-BOTTOM: #aaaaaa 1px solid; BACKGROUND-COLOR: #b0c4de
}
TABLE.head_title TD.title_button {
	OVERFLOW: hidden; WIDTH: 24px; BORDER-BOTTOM: #aaaaaa 1px solid; HEIGHT: 30px; TEXT-ALIGN: center
}
TABLE.head_title TD.title_sortbutton {
	BORDER-RIGHT: medium none; BORDER-TOP: medium none; OVERFLOW: hidden; BORDER-LEFT: medium none; WIDTH: 24px; BORDER-BOTTOM: medium none; HEIGHT: 25px; BACKGROUND-COLOR: #ffffff; TEXT-ALIGN: center
}
.table_detail TD.title_nine_char {
	BORDER-RIGHT: #aaaaaa 1px solid; OVERFLOW: hidden; WIDTH: 150px; BORDER-BOTTOM: #aaaaaa 1px solid
}
