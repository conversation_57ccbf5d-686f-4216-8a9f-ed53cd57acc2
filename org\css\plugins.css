.datepicker {
	padding: 0px; border-radius: 0px; border: 1px solid rgb(215, 215, 215); border-image: none; left: 0px; top: 0px; min-width: 230px; background-color: rgb(238, 238, 238);
}
.datepicker::before {
	left: 6px; top: -7px; border-right-color: transparent; border-bottom-color: rgb(215, 215, 215); border-left-color: transparent; border-right-width: 7px; border-bottom-width: 7px; border-left-width: 7px; border-right-style: solid; border-bottom-style: solid; border-left-style: solid; display: inline-block; position: absolute; content: "";
}
.datepicker::after {
	left: 7px; top: -6px; border-right-color: transparent; border-bottom-color: rgb(255, 255, 255); border-left-color: transparent; border-right-width: 6px; border-bottom-width: 6px; border-left-width: 6px; border-right-style: solid; border-bottom-style: solid; border-left-style: solid; display: inline-block; position: absolute; content: "";
}
.datepicker > div {
	display: none;
}
.datepicker table {
	margin: 0px; width: 100%;
}
.datepicker table thead tr:first-child {
	background: linear-gradient(rgb(253, 253, 253) 0%, rgb(248, 248, 248) 100%);
}
.datepicker table thead tr:nth-child(2) {
	background: linear-gradient(rgb(255, 255, 255) 0%, rgb(241, 241, 241) 100%);
}
.datepicker table tbody tr:nth-child(2n) {
	background-color: rgb(249, 249, 249);
}
.datepicker table tbody tr td:last-child {
	border-right-width: 0px !important;
}
.datepicker thead tr th:last-child {
	border-right-width: 0px !important;
}
.datepicker td {
	padding: 6px 10px; width: 20px; height: 20px; text-align: center;
}
.datepicker th {
	padding: 6px 10px; width: 20px; height: 20px; text-align: center;
}
.datepicker tbody td {
	border: 1px solid rgb(215, 215, 215); border-image: none; box-shadow: inset 0px 1px 0px #ffffff;
}
.datepicker thead tr:nth-child(2) th {
	border: 1px solid rgb(215, 215, 215); border-image: none; box-shadow: inset 0px 1px 0px #ffffff;
}
.datepicker td.day:hover {
	background: rgb(238, 238, 238); cursor: pointer;
}
.datepicker td.old {
	color: rgb(85, 85, 85);
}
.datepicker td.new {
	color: rgb(85, 85, 85);
}
.datepicker td.active {
	background: linear-gradient(rgb(255, 255, 255) 0%, rgb(241, 241, 241) 100%); color: rgb(0, 0, 0); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.datepicker td.active:hover {
	background: linear-gradient(rgb(255, 255, 255) 0%, rgb(241, 241, 241) 100%); color: rgb(0, 0, 0); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.datepicker td.active:hover {
	background-color: rgb(0, 68, 204);
}
.datepicker td.active:hover {
	background-color: rgb(0, 68, 204);
}
.datepicker td.active:active {
	background-color: rgb(0, 68, 204);
}
.datepicker td.active:hover:active {
	background-color: rgb(0, 68, 204);
}
.datepicker td.active.active {
	background-color: rgb(0, 68, 204);
}
.datepicker td.active.active:hover {
	background-color: rgb(0, 68, 204);
}
.datepicker td.disabled.active {
	background-color: rgb(0, 68, 204);
}
.datepicker td.disabled.active:hover {
	background-color: rgb(0, 68, 204);
}
.datepicker td[disabled].active {
	background-color: rgb(0, 68, 204);
}
.datepicker td[disabled].active:hover {
	background-color: rgb(0, 68, 204);
}
.datepicker td.active:active {
	
}
.datepicker td.active:hover:active {
	
}
.datepicker td.active.active {
	
}
.datepicker td.active.active:hover {
	
}
.datepicker td span {
	margin: 2px; border-radius: 4px; width: 47px; height: 54px; line-height: 54px; float: left; display: block; cursor: pointer; -webkit-border-radius: 4px; -moz-border-radius: 4px;
}
.datepicker td span:hover {
	background: rgb(238, 238, 238);
}
.datepicker td span.active {
	border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25); color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25); background-image: -ms-linear-gradient(rgb(0, 136, 204), rgb(0, 68, 204)); background-repeat: repeat-x; background-color: rgb(0, 109, 204);
}
.datepicker td span.active:hover {
	background-color: rgb(0, 68, 204);
}
.datepicker td span.active:active {
	background-color: rgb(0, 68, 204);
}
.datepicker td span.active.active {
	background-color: rgb(0, 68, 204);
}
.datepicker td span.disabled.active {
	background-color: rgb(0, 68, 204);
}
.datepicker td span[disabled].active {
	background-color: rgb(0, 68, 204);
}
.datepicker td span.active:active {
	
}
.datepicker td span.active.active {
	
}
.datepicker td span.old {
	color: rgb(119, 119, 119);
}
.datepicker th.switch {
	width: 60px;
}
.datepicker thead tr:first-child th {
	cursor: pointer;
}
.date.input-append .add-on i {
	width: 16px; height: 16px; display: block; cursor: pointer;
}
.date.input-prepend .add-on i {
	width: 16px; height: 16px; display: block; cursor: pointer;
}
[class*='ical_'] {
	background-position: 5px 12px; width: 20px; height: 35px; background-repeat: no-repeat; background-size: 10px auto;
}
.ical_left_arrow {
	background-image: url("../img/icons/gray/glyphicons_224_thin_arrow_left.png");
}
.ical_right_arrow {
	background-image: url("../img/icons/gray/glyphicons_223_thin_right_arrow.png");
}
.colorpicker-saturation {
	width: 100px; height: 100px; float: left; cursor: crosshair; background-image: url("../img/saturation.png");
}
.colorpicker-saturation i {
	margin: -4px 0px 0px -4px; border-radius: 5px; border: 1px solid rgb(0, 0, 0); border-image: none; left: 0px; top: 0px; width: 5px; height: 5px; display: block; position: absolute; -webkit-border-radius: 5px; -moz-border-radius: 5px;
}
.colorpicker-saturation i b {
	border-radius: 5px; border: 1px solid rgb(255, 255, 255); border-image: none; width: 5px; height: 5px; display: block; -webkit-border-radius: 5px; -moz-border-radius: 5px;
}
.colorpicker-hue {
	width: 15px; height: 100px; margin-bottom: 4px; margin-left: 4px; float: left; cursor: row-resize;
}
.colorpicker-alpha {
	width: 15px; height: 100px; margin-bottom: 4px; margin-left: 4px; float: left; cursor: row-resize;
}
.colorpicker-hue i {
	background: rgb(0, 0, 0); left: 0px; top: 0px; width: 100%; height: 1px; margin-top: -1px; border-top-color: rgb(255, 255, 255); border-top-width: 1px; border-top-style: solid; display: block; position: absolute;
}
.colorpicker-alpha i {
	background: rgb(0, 0, 0); left: 0px; top: 0px; width: 100%; height: 1px; margin-top: -1px; border-top-color: rgb(255, 255, 255); border-top-width: 1px; border-top-style: solid; display: block; position: absolute;
}
.colorpicker-hue {
	background-image: url("../img/hue.png");
}
.colorpicker-alpha {
	display: none; background-image: url("../img/alpha.png");
}
.colorpicker {
	padding: 4px; border-radius: 4px; left: 0px; top: 0px; margin-top: 1px; min-width: 120px; -webkit-border-radius: 4px; -moz-border-radius: 4px;
}
.colorpicker::before {
	display: table; content: "";
}
.colorpicker::after {
	display: table; content: "";
}
.colorpicker::after {
	clear: both;
}
.colorpicker::before {
	left: 6px; top: -7px; border-right-color: transparent; border-bottom-color: rgba(0, 0, 0, 0.2); border-left-color: transparent; border-right-width: 7px; border-bottom-width: 7px; border-left-width: 7px; border-right-style: solid; border-bottom-style: solid; border-left-style: solid; display: inline-block; position: absolute; content: "";
}
.colorpicker::after {
	left: 7px; top: -6px; border-right-color: transparent; border-bottom-color: rgb(255, 255, 255); border-left-color: transparent; border-right-width: 6px; border-bottom-width: 6px; border-left-width: 6px; border-right-style: solid; border-bottom-style: solid; border-left-style: solid; display: inline-block; position: absolute; content: "";
}
.colorpicker div {
	position: relative;
}
.alpha.colorpicker {
	min-width: 140px;
}
.alpha.colorpicker .colorpicker-alpha {
	display: block;
}
.colorpicker-color {
	background-position: 0px 100%; height: 10px; clear: both; margin-top: 5px; background-image: url("../img/alpha.png");
}
.colorpicker-color div {
	height: 10px;
}
.color.input-append .add-on i {
	width: 16px; height: 16px; display: block; cursor: pointer;
}
.color.input-prepend .add-on i {
	width: 16px; height: 16px; display: block; cursor: pointer;
}
div.selector {
	margin: -5px 0px; padding: 0px 0px 0px 8px; border-radius: 2px; border: 1px solid rgb(210, 210, 210); border-image: none; width: auto; height: 27px; line-height: 27px; overflow: visible; font-size: 12px; vertical-align: middle; display: inline-block; position: relative; cursor: pointer; max-width: 280px; box-sizing: content-box; box-shadow: inset 0px 1px 0px #ffffff, 0px 1px 0px #eeeeee; -webkit-box-sizing: content-box; -moz-box-sizing: content-box; -ms-box-sizing: content-box; -webkit-box-shadow: 0 1px 0 #FFFFFF inset, 0 1px 0 #EEEEEE;
}
div.selector span {
	background: url("../img/selectArrow.png") no-repeat 100%; padding: 0px 37px 0px 2px; height: 27px; line-height: 27px; overflow: hidden; font-size: 11px; display: block; white-space: nowrap; cursor: pointer; -ms-text-overflow: ellipsis; text-shadow: 0px 1px #f5f5f5; -o-text-overflow: ellipsis;
}
div.selector select {
	background: none; border: 1px solid white; border-image: none; left: 0px; top: 0px; width: 100%; height: 28px; font-size: 12px; position: absolute; opacity: 0;
}
.chzn-container {
	display: inline-block; position: relative; -ms-zoom: 1; max-width: 100%;
}
.noSearch .chzn-search {
	display: none;
}
.searchDrop .selector {
	display: none;
}
.noSearch .selector {
	display: none;
}
.noSearch .chzn-results {
	margin: 0px !important; padding: 2px !important;
}
.chzn-container > .chzn-drop {
	background: rgb(255, 255, 255); border-width: 0px 1px 1px; border-style: none solid solid; border-color: currentColor rgb(213, 213, 213) rgb(213, 213, 213); border-image: none; left: 0px; top: 29px; width: 100% !important; margin-top: 1px; position: absolute; z-index: 998; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
.chzn-container-single .chzn-single {
	background: -ms-linear-gradient(rgb(252, 252, 252) 0%, rgb(241, 241, 241) 100%); padding: 0px 0px 0px 10px; border-radius: 2px; border: 1px solid rgb(210, 210, 210); border-image: none; height: 27px; color: rgb(128, 128, 128); line-height: 27px; overflow: hidden; display: block; white-space: nowrap; position: relative; box-sizing: content-box; box-shadow: inset 0px 1px 0px #fff, 0px 1px 0px #eeeeee; text-shadow: 0px 1px #f5f5f5; -webkit-box-sizing: content-box; -moz-box-sizing: content-box; -ms-box-sizing: content-box; -webkit-box-shadow: 0 1px 0 #fff inset, 0 1px 0px #eeeeee;
}
.chzn-container-single .chzn-single span {
	background: url("../img/selectArrow.png") no-repeat 100%; overflow: hidden; padding-right: 30px; font-size: 11px; display: block; white-space: nowrap; -ms-text-overflow: ellipsis; -o-text-overflow: ellipsis;
}
.chzn-container-single .chzn-single abbr {
	top: 6px; width: 12px; height: 13px; right: 26px; font-size: 1px; display: block; position: absolute;
}
.chzn-container-single .chzn-single div {
	top: -1px; width: 27px; height: 27px; right: -1px; display: block; position: absolute;
}
.chzn-container-single .chzn-single div b {
	width: 27px; height: 28px; display: block;
}
.chzn-container-single .chzn-search {
	margin: 0px; padding: 3px 4px; white-space: nowrap; position: relative; z-index: 1010;
}
.chzn-container-single .chzn-search input {
	background: url("../img/searchSmall.png") no-repeat 98% !important; margin: 1px 0px; padding: 4px 20px 4px 5px; outline: 0px; border: 1px solid rgb(170, 170, 170); border-image: none; font-family: sans-serif; font-size: 11px; min-width: 100%; max-width: 100%; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
.chzn-container-single-nosearch .chzn-search input {
	left: -9000px; position: absolute;
}
.chzn-container-multi .chzn-choices {
	margin: 0px; border: 1px solid rgb(215, 215, 215); border-image: none; height: auto !important; color: rgb(119, 119, 119); overflow: hidden; font-size: 12px; position: relative; cursor: text;
}
.chzn-container-multi .chzn-choices li {
	list-style: none; float: left;
}
.chzn-container-multi .chzn-choices .search-field {
	margin: 0px; padding: 0px; white-space: nowrap;
}
.chzn-container-multi .chzn-choices .search-field input {
	background: none !important; margin: 0px; padding: 5px !important; outline: 0px; border: 0px currentColor !important; border-image: none !important; color: rgb(119, 119, 119); font-size: 12px !important; box-shadow: none !important; -webkit-box-shadow: none;
}
.chzn-container-multi .chzn-choices .search-field .default {
	color: rgb(119, 119, 119);
}
.chzn-container-multi .chzn-choices .search-choice {
	background: rgb(248, 248, 248); margin: 4px; padding: 5px 24px 5px 8px; border: 1px solid rgb(215, 215, 215); border-image: none; line-height: 16px; font-size: 11px; float: left; display: block; position: relative;
}
.chzn-container-multi .chzn-choices .search-choice-focus {
	background: rgb(212, 212, 212);
}
.chzn-container-multi .chzn-choices .search-choice .search-choice-close {
	background: url("../img/closeSelection.png") no-repeat 50%; top: 8px; width: 10px; height: 10px; right: 6px; font-size: 1px; display: block; position: absolute;
}
.chzn-container-multi .chzn-choices .search-choice-focus .search-choice-close {
	background-position: right -11px;
}
.chzn-container .chzn-results {
	margin: 0px 4px 4px 0px; padding: 0px 0px 0px 4px; position: relative; -ms-overflow-x: hidden; max-height: 240px;
}
.chzn-container-multi .chzn-results {
	margin: 0px; padding: 0px;
}
.chzn-container .chzn-results li {
	list-style: none; margin: 0px; padding: 5px 6px; line-height: 14px; font-size: 11px; display: none;
}
.chzn-container .chzn-results .active-result {
	display: list-item; cursor: pointer;
}
.chzn-container .chzn-results .highlighted {
	color: rgb(255, 255, 255); background-color: rgb(56, 117, 215);
}
.chzn-container .chzn-results li em {
	background: rgb(254, 255, 222); font-style: normal;
}
.chzn-container .chzn-results .highlighted em {
	background: none;
}
.chzn-container .chzn-results .no-results {
	background: rgb(244, 244, 244); display: list-item;
}
.chzn-container .chzn-results .group-result {
	color: rgb(46, 116, 166); font-size: 10px; font-weight: bold; border-top-color: rgb(221, 221, 221); border-bottom-color: rgb(221, 221, 221); border-top-width: 1px; border-bottom-width: 1px; border-top-style: solid; border-bottom-style: solid; cursor: default;
}
.chzn-container .chzn-results .group-option {
	padding-left: 15px;
}
.chzn-container-multi .chzn-drop .result-selected {
	display: none;
}
.chzn-container .chzn-results-scroll {
	background: white; margin: 0px 4px; width: 321px; text-align: center; position: absolute; z-index: 1;
}
.chzn-container .chzn-results-scroll span {
	width: 9px; height: 17px; text-indent: -5000px; display: inline-block;
}
.chzn-container .chzn-results-scroll-down {
	bottom: 0px;
}
.chzn-container-active .chzn-single-with-drop div {
	background: none; border-left-color: currentColor; border-left-width: medium; border-left-style: none;
}
.chzn-container-active .chzn-choices {
	border: 1px solid rgb(213, 213, 213); border-image: none;
}
.chzn-container-active .chzn-choices .search-field input {
	color: rgb(17, 17, 17) !important;
}
.chzn-disabled {
	cursor: default; opacity: 0.5 !important;
}
.chzn-disabled .chzn-single {
	cursor: default;
}
.chzn-disabled .chzn-choices .search-choice .search-choice-close {
	cursor: default;
}
.whead .on_off {
	padding: 9px 9px 7px; float: right;
}
.whead .on_off .ibutton-container {
	float: right;
}
.whead .on_off [class^='icon-'] {
	margin-top: 3px; float: left; display: block;
}
.whead .on_off [class*=' icon-'] {
	margin-top: 3px; float: left; display: block;
}
.on_off .ibutton-label-off span {
	background: url("../img/no.png") no-repeat; left: -4px; width: 8px; height: 9px; text-indent: -9999px; padding-top: 0px; padding-right: 10px; margin-top: 3px; float: right; display: block; position: relative;
}
.on_off .ibutton-label-on span {
	background: url("../img/yes.png") no-repeat; left: -1px; width: 11px; height: 11px; padding-top: 0px; padding-right: 15px; margin-top: 1px; float: right; display: block; position: relative;
}
.on_off .ibutton-label-off {
	background-position: 100% 0px; width: 95%; text-align: right; right: 0px; color: rgb(255, 255, 255); text-shadow: 0px 1px #808080;
}
.on_off .ibutton-container {
	height: 21px;
}
.on_off .ibutton-label-on {
	background-position: 0px -52px; left: 0px; color: rgb(255, 255, 255); z-index: 1; text-shadow: 0px 1px #808080;
}
.on_off .ibutton-label-off {
	background-position: 100% -52px;
}
.on_off .ibutton-label-on {
	height: 16px;
}
.on_off .ibutton-label-off {
	height: 16px;
}
.on_off .ibutton-handle {
	background-position: 0px -73px; height: 20px;
}
.on_off .ibutton-handle-right {
	background: url("../img/ibutton-slider-default.png") no-repeat 100% -73px;
}
.on_off .ibutton-handle-middle {
	background: url("../img/ibutton-slider-default.png") no-repeat 50% -73px;
}
.on_off .ibutton-padding-left {
	background: url("../img/ibutton-slider-default.png") no-repeat 0px -52px; top: 0px; width: 3px; height: 20px; position: absolute; z-index: 2;
}
.on_off .ibutton-padding-right {
	background: url("../img/ibutton-slider-default.png") no-repeat 0px -52px; top: 0px; width: 3px; height: 20px; position: absolute; z-index: 2;
}
.on_off .ibutton-padding-right {
	background-position: 100% -52px;
}
.ibutton-container {
	width: 89px; height: 26px; overflow: hidden; margin-left: 5px; float: left; display: inline; position: relative; cursor: pointer; max-width: 400px; -ms-user-select: none; -webkit-user-select: none; -moz-user-select: none; user-select: none; -moz-user-focus: ignore; -moz-user-input: disabled;
}
.ibutton-container input {
	left: 0px; top: 0px; position: absolute; opacity: 0; -moz-user-input: enabled; -moz-opacity: 0.0;
}
.ibutton-handle {
	background: url("../img/ibutton-slider-default.png") no-repeat 0px -26px; left: 0px; top: 0px; width: 33px; height: 25px; padding-left: 3px; display: block; position: absolute; z-index: 3; cursor: inherit;
}
.ibutton-handle-right {
	background: url("../img/ibutton-slider-default.png") no-repeat 100% -26px; width: 100%; height: 100%; padding-right: 3px; z-index: 3;
}
.ibutton-handle-middle {
	background: url("../img/ibutton-slider-default.png") no-repeat 50% -26px; width: 100%; height: 100%; z-index: 3;
}
div.ibutton-label-on {
	background: url("../img/ibutton-slider-default.png") no-repeat 0px 0px; top: 0px; width: auto; height: 26px; text-align: center; text-transform: uppercase; line-height: 18px; overflow: hidden; padding-top: 4px; font-size: 11px; font-weight: bold; display: block; white-space: nowrap; position: absolute; cursor: inherit;
}
div.ibutton-label-off {
	background: url("../img/ibutton-slider-default.png") no-repeat 0px 0px; top: 0px; width: auto; height: 26px; text-align: center; text-transform: uppercase; line-height: 18px; overflow: hidden; padding-top: 4px; font-size: 11px; font-weight: bold; display: block; white-space: nowrap; position: absolute; cursor: inherit;
}
div.ibutton-label-on {
	left: 0px; color: rgb(255, 255, 255); z-index: 1; text-shadow: 0px 1px #808080;
}
div.ibutton-label-on span {
	padding-left: 0px;
}
div.ibutton-label-off {
	background-position: 100% 0px; width: 95%; text-align: right; right: 0px; color: rgb(255, 255, 255); text-shadow: 0px 1px #808080;
}
div.ibutton-label-off span label {
	padding-right: 30px; margin-left: -2px;
}
.yes_no .ibutton-label-off span label {
	padding-right: 15px; margin-left: -2px;
}
.ibutton-padding-left {
	left: 0px;
}
.ibutton-padding-right {
	right: 0px;
}
.ibutton-disabled {
	cursor: not-allowed !important; opacity: 0.6;
}
.ui-spinner {
	padding: 0px 6px; border: 1px solid rgb(215, 215, 215); border-image: none; width: 15em; height: 26px; overflow: hidden; display: block; position: relative; box-shadow: 0px 1px 0px #fff; -webkit-box-shadow: 0 1px 0 #fff;
}
.ui-spinner-disabled {
	background: rgb(244, 244, 244); color: rgb(204, 204, 204);
}
.ui-spinner input.ui-spinner-box {
	background: none !important; padding: 0px !important; border: currentColor !important; border-image: none !important; height: 26px;
}
.ui-spinner-up {
	margin: 0px; padding: 0px; border: currentColor; border-image: none; width: 18px; right: 0px; position: absolute; z-index: 100; cursor: pointer;
}
.ui-spinner-down {
	margin: 0px; padding: 0px; border: currentColor; border-image: none; width: 18px; right: 0px; position: absolute; z-index: 100; cursor: pointer;
}
.ui-spinner-up {
	background: url("../img/spinnerTop.png") no-repeat; top: -1px; height: 14px;
}
.ui-spinner-down {
	background: url("../img/spinnerBottom.png") no-repeat; height: 14px; bottom: -1px;
}
.ui-spinner-list {
	margin: 0px; padding: 0px; font-size: 11px;
}
.ui-spinner-listitem {
	margin: 0px; padding: 0px; font-size: 11px;
}
.ui-spinner ul li {
	height: 26px; line-height: 26px;
}
.ui-spinner-data {
	height: 26px; line-height: 26px;
}
.inputContainer {
	float: left; position: relative;
}
.formError {
	left: 282px; top: 300px; display: block; position: absolute; z-index: 998; cursor: pointer;
}
.ajaxSubmit {
	background: rgb(85, 234, 85); padding: 20px; border: 1px solid rgb(153, 153, 153); border-image: none; display: none;
}
.formError .formErrorContent {
	background: linear-gradient(rgba(248, 248, 248, 1) 0%, rgba(232, 232, 232, 1) 100%); padding: 2px 10px; border-radius: 3px; border: 1px solid rgb(215, 215, 215); border-image: none; width: 124px; font-size: 11px; position: relative; z-index: 5001;
}
.greenPopup .formErrorContent {
	background: rgb(51, 190, 64);
}
.blackPopup .formErrorContent {
	background: rgb(57, 57, 57); color: rgb(255, 255, 255);
}
.formError .formErrorArrow {
	margin: -2px auto 0px; width: 15px; position: relative; z-index: 5006;
}
.formError .formErrorArrowBottom {
	margin: 0px 0px 0px 12px; top: 2px; box-shadow: none; -webkit-box-shadow: none;
}
.formError .formErrorArrow div {
	background: rgb(232, 232, 232); margin: 0px auto; height: 1px; line-height: 0; font-size: 0px; border-right-color: rgb(215, 215, 215); border-left-color: rgb(215, 215, 215); border-right-width: 1px; border-left-width: 1px; border-right-style: solid; border-left-style: solid; display: block;
}
.formError .formErrorArrowBottom div {
	box-shadow: none; -webkit-box-shadow: none;
}
.greenPopup .formErrorArrow div {
	background: rgb(51, 190, 64);
}
.blackPopup .formErrorArrow div {
	background: rgb(57, 57, 57); color: rgb(255, 255, 255);
}
.formError .formErrorArrow .line10 {
	border: currentColor; border-image: none; width: 15px;
}
.formError .formErrorArrow .line9 {
	border: currentColor; border-image: none; width: 13px;
}
.formError .formErrorArrow .line8 {
	width: 11px;
}
.formError .formErrorArrow .line7 {
	width: 9px;
}
.formError .formErrorArrow .line6 {
	width: 7px;
}
.formError .formErrorArrow .line5 {
	width: 5px;
}
.formError .formErrorArrow .line4 {
	width: 3px;
}
.formError .formErrorArrow .line3 {
	width: 1px; border-right-color: rgb(221, 221, 221); border-bottom-color: rgb(221, 221, 221); border-left-color: rgb(221, 221, 221); border-right-width: 2px; border-bottom-width: 0px; border-left-width: 2px; border-right-style: solid; border-bottom-style: solid; border-left-style: solid;
}
.formError .formErrorArrow .line2 {
	background: rgb(221, 221, 221); border: currentColor; border-image: none; width: 3px;
}
.formError .formErrorArrow .line1 {
	background: rgb(221, 221, 221); border: currentColor; border-image: none; width: 1px;
}
.checker input {
	cursor: pointer;
}
.radio input {
	cursor: pointer;
}
.selector select {
	cursor: pointer;
}
input.error {
	border-color: rgb(221, 190, 190) !important;
}
.wizard .stepContainer {
	margin: 0px; overflow: hidden; clear: both; border-top-color: rgb(215, 215, 215); border-top-width: 1px; border-top-style: solid; display: block; position: relative;
}
.wizard .stepContainer div.content {
	margin: 0px; width: 100%; text-align: left; color: rgb(85, 85, 85); overflow: visible; clear: both; float: left; display: block; position: absolute; z-index: 88; min-height: 200px; background-color: rgb(248, 248, 248);
}
.wizard div.actionBar {
	margin: 0px 0px -18px; left: 0px; height: 40px; text-align: left; overflow: auto; padding-right: 55px; clear: both; border-top-color: rgb(215, 215, 215); border-top-width: 1px; border-top-style: solid; display: block; position: relative; z-index: 88; background-color: rgb(232, 232, 232);
}
.wizard ul.anchor {
	list-style: none; margin: 0px; height: 70px; border-bottom-color: rgb(215, 215, 215); border-bottom-width: 1px; border-bottom-style: solid; box-shadow: 0px 1px 0px #ffffff;
}
.wizard ul.anchor li {
	margin: -1px 0px 0px; text-align: center; float: left; display: block; position: relative;
}
.wizard ul.anchor li a {
	margin: 0px; padding: 15px 0px 0px; text-decoration: none; float: left; display: block; position: relative; z-index: 5; min-height: 60px; min-width: 230px; outline-style: none;
}
.wizard ul.anchor li a .stepNumber {
	font: bold 45px/normal Verdana, Arial, Helvetica, sans-serif; padding: 0px 5px 5px; width: 30px; text-align: center; float: left; position: relative; font-size-adjust: none; font-stretch: normal;
}
.wizard ul.anchor li a .stepDesc {
	padding: 5px; text-align: center; font-size: 18px; position: relative; text-shadow: 0px 1px #ffffff;
}
.wizard ul.anchor li a .stepDesc small {
	font-size: 12px;
}
.wizard ul.anchor li a.selected {
	color: rgb(119, 119, 119);
}
.wizard ul.anchor li a.selected:hover {
	color: rgb(184, 184, 184);
}
.wizard ul.anchor li a.done {
	color: rgb(70, 115, 2); position: relative; z-index: 5;
}
.wizard ul.anchor li a.done:hover {
	color: rgb(119, 119, 119);
}
.wizard ul.anchor li a.disabled {
	color: rgb(184, 184, 184); cursor: text;
}
.wizard ul.anchor li a.disabled:hover {
	color: rgb(184, 184, 184);
}
.wizard ul.anchor li a.error {
	background: rgb(240, 143, 117) !important; border: 1px solid rgb(190, 74, 47) !important; border-image: none !important; color: rgb(108, 108, 108) !important;
}
.wizard ul.anchor li a.error:hover {
	color: rgb(0, 0, 0) !important;
}
.wizard .buttonNext {
	margin: 5px 3px 0px; padding: 5px 25px; text-align: center; text-decoration: none; float: right; display: block; outline-style: none;
}
.wizard .buttonPrevious {
	margin: 5px 3px 0px; padding: 5px 25px; text-align: center; text-decoration: none; float: right; display: block;
}
.wizard .buttonFinish {
	margin: 5px 10px 0px 3px; padding: 5px 25px; text-align: center; text-decoration: none; float: right; display: block;
}
.txtBox {
	padding: 2px; border: 1px solid rgb(215, 215, 215); border-image: none; width: 100%; color: rgb(90, 86, 85);
}
.txtBox:focus {
	border: 1px solid rgb(215, 215, 215); border-image: none;
}
.wizard .loader {
	background: url("../img/loading.gif") no-repeat 5px rgb(255, 255, 255); margin: 2px 0px 0px 2px; padding: 8px 10px 8px 40px; border: 1px solid rgb(215, 215, 215); border-image: none; color: rgb(119, 119, 119); float: left; display: none; position: relative; z-index: 998;
}
.wizard .msgBox {
	margin: 4px 0px 0px 5px; padding: 5px; border: 1px solid rgb(215, 215, 215); border-image: none; color: rgb(119, 119, 119); float: left; display: none; position: relative; z-index: 999; min-width: 200px;
}
.wizard .msgBox .content {
	padding: 0px; float: left;
}
.wizard .msgBox .close {
	margin: 0px 0px 0px 5px; padding: 0px 2px; border-radius: 3px; border: 1px solid rgb(215, 215, 215); border-image: none; text-align: center; color: rgb(119, 119, 119); text-decoration: none; float: right; display: block; position: relative; outline-style: none;
}
.wizard .msgBox .close:hover {
	border: 1px solid rgb(215, 215, 215); border-image: none; color: rgb(119, 119, 119);
}
.swMain {
	min-height: 300px;
}
.swMain .stepContainer {
	margin: 0px; padding: 0px; overflow: hidden; clear: right; display: block; position: relative;
}
.swMain .stepContainer div.content {
	margin: 0px; padding: 0px; width: 100%; text-align: left; color: rgb(85, 85, 85); overflow: auto; clear: both; float: left; display: block; position: absolute; z-index: 88; min-height: 200px; background-color: rgb(248, 248, 248);
}
.swMain div.actionBar {
	margin: 3px 0px 0px; height: 40px; text-align: left; overflow: auto; padding-right: 55px; clear: right; display: block; position: relative; z-index: 88;
}
.swMain .stepContainer .StepTitle {
	font: bold 16px/normal Verdana, Arial, Helvetica, sans-serif; margin: 0px; padding: 5px; border: 1px solid rgb(224, 224, 224); border-image: none; text-align: left; color: rgb(90, 86, 85); clear: both; display: block; position: relative; z-index: 88; font-size-adjust: none; font-stretch: normal; background-color: rgb(224, 224, 224); -webkit-border-radius: 5px; -moz-border-radius: 5px;
}
.swMain ul.anchor {
	list-style: none; margin: 0px;
}
.swMain ul.anchor li {
	margin: -1px 0px 0px; text-align: center; clear: both; border-right-color: rgb(215, 215, 215); border-bottom-color: rgb(215, 215, 215); border-right-width: 1px; border-bottom-width: 1px; border-right-style: solid; border-bottom-style: solid; float: left; display: block; position: relative;
}
.swMain ul.anchor li:last-child {
	border-bottom-color: currentColor; border-bottom-width: medium; border-bottom-style: none;
}
.swMain ul.anchor li a {
	margin: 0px; padding: 15px 0px 0px; text-decoration: none; float: left; display: block; position: relative; z-index: 99; min-height: 56px; min-width: 230px; outline-style: none;
}
.swMain ul.anchor li a .stepNumber {
	padding: 0px 5px 5px; width: 30px; text-align: center; float: left; position: relative;
}
.swMain ul.anchor li a .stepDesc {
	padding: 5px; text-align: center; font-size: 18px; position: relative; text-shadow: 0px 1px #ffffff;
}
.swMain ul.anchor li a .stepDesc small {
	font-size: 12px;
}
.swMain ul.anchor li a.selected {
	color: rgb(119, 119, 119);
}
.swMain ul.anchor li a.selected:hover {
	color: rgb(184, 184, 184);
}
.swMain ul.anchor li a.done {
	color: rgb(70, 115, 2); position: relative; z-index: 99;
}
.swMain ul.anchor li a.done:hover {
	color: rgb(119, 119, 119);
}
.swMain ul.anchor li a.disabled {
	color: rgb(184, 184, 184); cursor: text;
}
.swMain ul.anchor li a.disabled:hover {
	color: rgb(184, 184, 184);
}
.swMain ul.anchor li a.error {
	background: rgb(240, 143, 117) !important; border: 1px solid rgb(190, 74, 47) !important; border-image: none !important; color: rgb(108, 108, 108) !important;
}
.swMain ul.anchor li a.error:hover {
	color: rgb(0, 0, 0) !important;
}
.swMain .buttonNext {
	margin: 5px 3px 0px; padding: 5px; text-align: center; text-decoration: none; float: right; display: block; outline-style: none;
}
.swMain .buttonPrevious {
	margin: 5px 3px 0px; padding: 5px; text-align: center; color: rgb(119, 119, 119); text-decoration: none; float: right; display: block; outline-style: none;
}
.swMain .buttonFinish {
	margin: 5px 10px 0px 3px; padding: 5px; text-align: center; text-decoration: none; float: right; display: block; outline-style: none;
}
.txtBox {
	padding: 2px; border: 1px solid rgb(204, 204, 204); border-image: none; width: 430px; color: rgb(90, 86, 85);
}
.txtBox:focus {
	border: 1px solid rgb(234, 133, 17); border-image: none;
}
.swMain .loader {
	background: url("../img/loading.gif") no-repeat 5px rgb(255, 255, 255); margin: 2px 0px 0px 2px; padding: 8px 10px 8px 40px; border: 1px solid rgb(255, 215, 0); border-image: none; color: rgb(90, 86, 85); float: left; display: none; position: relative; z-index: 998;
}
.swMain .msgBox {
	margin: 4px 0px 0px 5px; padding: 5px; border: 1px solid rgb(255, 215, 0); border-image: none; color: rgb(90, 86, 85); float: left; display: none; position: relative; z-index: 999; min-width: 200px; background-color: rgb(255, 255, 221);
}
.swMain .msgBox .content {
	padding: 0px; float: left;
}
.swMain .msgBox .close {
	margin: 0px 0px 0px 5px; padding: 0px 2px; border-radius: 3px; border: 1px solid rgb(204, 204, 204); border-image: none; text-align: center; color: rgb(204, 204, 204); text-decoration: none; float: right; display: block; position: relative; outline-style: none;
}
.swMain .msgBox .close:hover {
	border: 1px solid rgb(234, 133, 17); border-image: none; color: rgb(234, 133, 17);
}
.cleditorMain {
	min-height: 250px;
}
.cleditorMain iframe {
	margin: 0px; padding: 0px; border: currentColor; border-image: none;
}
.cleditorMain textarea {
	margin: 0px; padding: 0px; border: currentColor; border-image: none; -ms-overflow-y: scroll; -webkit-resize: none; -moz-resize: none; resize: none;
}
.cleditorToolbar {
	background: url("../img/toolbar.png");
}
.cleditorGroup {
	height: 26px; float: left;
}
.cleditorButton {
	background: url("../img/buttons.gif"); margin: 1px 0px; width: 24px; height: 24px; float: left;
}
.cleditorDisabled {
	opacity: 0.3;
}
.cleditorDivider {
	background: rgb(204, 204, 204); margin: 1px 0px; width: 1px; height: 23px; float: left;
}
.cleditorPopup {
	font: 10pt/normal Cuprum, sans-serif; border: 1px solid rgb(153, 153, 153); border-image: none; position: absolute; z-index: 10000; cursor: default; font-size-adjust: none; font-stretch: normal; background-color: white;
}
.cleditorList div {
	padding: 2px 4px;
}
.cleditorList p {
	margin: 0px; padding: 0px; background-color: transparent;
}
.cleditorList h1 {
	margin: 0px; padding: 0px; background-color: transparent;
}
.cleditorList h2 {
	margin: 0px; padding: 0px; background-color: transparent;
}
.cleditorList h3 {
	margin: 0px; padding: 0px; background-color: transparent;
}
.cleditorList h4 {
	margin: 0px; padding: 0px; background-color: transparent;
}
.cleditorList h5 {
	margin: 0px; padding: 0px; background-color: transparent;
}
.cleditorList h6 {
	margin: 0px; padding: 0px; background-color: transparent;
}
.cleditorList font {
	margin: 0px; padding: 0px; background-color: transparent;
}
.cleditorColor {
	padding: 1px 0px 0px 1px; width: 150px;
}
.cleditorColor div {
	margin: 0px 1px 1px 0px; width: 14px; height: 14px; float: left;
}
.cleditorPrompt {
	padding: 4px; font-size: 8.5pt; background-color: rgb(246, 247, 249);
}
.cleditorPrompt input {
	font: 8.5pt/normal Cuprum, sans-serif; font-size-adjust: none; font-stretch: normal;
}
.cleditorPrompt textarea {
	font: 8.5pt/normal Cuprum, sans-serif; font-size-adjust: none; font-stretch: normal;
}
.cleditorMsg {
	padding: 4px; width: 150px; font-size: 8.5pt; background-color: rgb(253, 252, 238);
}
div.checker {
	width: 16px; height: 16px; margin-top: 2px; vertical-align: middle; float: left; display: block; position: relative; -ms-zoom: 1; box-shadow: 0px 1px 0px #fff; -webkit-box-shadow: 0 1px 0 #fff;
}
div.checker input {
	background: none; width: 16px; height: 16px; display: inline-block; opacity: 0;
}
div.checker span {
	background: url("../img/checkboxes.png") no-repeat 0px 0px; width: 16px; height: 16px; text-align: center; display: inline-block;
}
div.checker span.checked {
	background-position: 0px -17px;
}
table.dataTable {
	margin: 0px auto; width: 100%; clear: both;
}
table.dataTable thead th {
	padding: 7px 18px 7px 10px; color: rgb(85, 85, 85); font-size: 13px; font-weight: normal; cursor: pointer; box-shadow: inset 0px 1px 0px #fff; -webkit-box-shadow: 0 1px 0px #fff inset;
}
table.dataTable tfoot th {
	padding: 3px 18px 3px 10px; font-weight: bold;
}
table.dataTable td.center {
	text-align: center;
}
table.dataTable td.dataTables_empty {
	text-align: center;
}
table.dataTable tbody tr td {
	border-left-color: rgb(223, 223, 223); border-left-width: 1px; border-left-style: solid;
}
table.dataTable thead tr th {
	border-left-color: rgb(223, 223, 223); border-left-width: 1px; border-left-style: solid;
}
table.dataTable tbody tr td:first-child {
	border-left-color: currentColor; border-left-width: medium; border-left-style: none;
}
table.dataTable thead tr th:first-child {
	border-left-color: currentColor; border-left-width: medium; border-left-style: none;
}
table.dataTable thead tr:first-child {
	margin: 0px; padding: 0px; border-top-color: currentColor; border-top-width: medium; border-top-style: none;
}
table.dataTable tr {
	border-top-color: rgb(223, 223, 223); border-top-width: 1px; border-top-style: solid;
}
table.dataTable tr.even {
	background-color: rgb(242, 242, 242);
}
tr.gradeA.odd td.sorting_1 {
	background-color: transparent;
}
tr.gradeA.odd td.sorting_2 {
	background-color: rgb(209, 255, 209);
}
tr.gradeA.odd td.sorting_3 {
	background-color: rgb(209, 255, 209);
}
tr.gradeA.even td.sorting_1 {
	background-color: rgb(239, 239, 239);
}
tr.gradeA.even td.sorting_2 {
	background-color: rgb(226, 255, 226);
}
tr.gradeA.even td.sorting_3 {
	background-color: rgb(226, 255, 226);
}
tr.gradeC.odd td.sorting_1 {
	background-color: rgb(196, 196, 255);
}
tr.gradeC.odd td.sorting_2 {
	background-color: rgb(209, 209, 255);
}
tr.gradeC.odd td.sorting_3 {
	background-color: rgb(209, 209, 255);
}
tr.gradeC.even td.sorting_1 {
	background-color: rgb(213, 213, 255);
}
tr.gradeC.even td.sorting_2 {
	background-color: rgb(226, 226, 255);
}
tr.gradeC.even td.sorting_3 {
	background-color: rgb(226, 226, 255);
}
.dataTables_wrapper {
	clear: both; position: relative;
}
.dataTables_length {
	margin: 15px 10px 0px 0px; float: right;
}
.showentries {
	margin: 4px 15px 0px 0px; float: left;
}
.dataTables_filter {
	margin: 10px 0px 10px 10px; text-align: left; font-size: 11px; float: left; position: relative;
}
.dataTables_filter > label {
	margin-bottom: 0px;
}
.dataTables_length > label {
	margin-bottom: 0px;
}
.dataTables_filter label > span {
	padding-top: 1px; float: left; display: block;
}
.dataTables_filter input[type=text] {
	margin: 0px 0px 0px 5px;
}
.dataTables_info {
	margin: 17px 0px 0px; clear: both; float: left;
}
.tableFooter {
	background: linear-gradient(rgb(248, 248, 248) 0%, rgb(239, 239, 239) 100%); padding: 8px 12px; height: 36px; color: rgb(99, 99, 99); clear: both; font-size: 11px; border-top-color: rgb(205, 205, 205); border-top-width: 1px; border-top-style: solid; -webkit-border-bottom-left-radius: 3px; -webkit-border-bottom-right-radius: 3px; border-radius-bottomright: 3px; border-radius-bottomleft: 3px;
}
.dataTables_paginate {
	margin: 15px 0px; float: right;
}
.dataTables_paginate .last {
	margin-right: 0px !important;
}
.paginate_disabled_previous {
	height: 19px; color: rgb(17, 17, 17) !important; float: left; cursor: pointer;
}
.paginate_enabled_previous {
	height: 19px; color: rgb(17, 17, 17) !important; float: left; cursor: pointer;
}
.paginate_disabled_next {
	height: 19px; color: rgb(17, 17, 17) !important; float: left; cursor: pointer;
}
.paginate_enabled_next {
	height: 19px; color: rgb(17, 17, 17) !important; float: left; cursor: pointer;
}
.paginate_disabled_previous:hover {
	text-decoration: none !important;
}
.paginate_enabled_previous:hover {
	text-decoration: none !important;
}
.paginate_disabled_next:hover {
	text-decoration: none !important;
}
.paginate_enabled_next:hover {
	text-decoration: none !important;
}
.paginate_disabled_previous:active {
	
}
.paginate_enabled_previous:active {
	
}
.paginate_disabled_next:active {
	
}
.paginate_enabled_next:active {
	
}
.paginate_disabled_previous {
	color: rgb(102, 102, 102) !important;
}
.paginate_disabled_next {
	color: rgb(102, 102, 102) !important;
}
.paginate_disabled_previous {
	padding-left: 23px;
}
.paginate_enabled_previous {
	padding-left: 23px;
}
.paginate_disabled_next {
	margin-left: 10px;
}
.paginate_enabled_next {
	margin-left: 10px;
}
.paging_full_numbers {
	height: 22px; line-height: 22px;
}
.paging_full_numbers .next:active {
	font-weight: normal !important;
}
.paging_full_numbers .first:active {
	font-weight: normal !important;
}
.paging_full_numbers .previous:active {
	font-weight: normal !important;
}
.paging_full_numbers .last:active {
	font-weight: normal !important;
}
.paging_full_numbers a:active {
	
}
.paging_full_numbers a:hover {
	text-decoration: none;
}
.paging_full_numbers a.paginate_button {
	margin: 0px 3px; padding: 4px 7px; border-radius: 3px; border: 1px solid rgb(211, 211, 211); border-image: none; color: rgb(85, 85, 85); font-size: 11px; cursor: pointer; box-shadow: inset 0px 1px 0px #fff, 0px 1px 0px #dfdfdf; -webkit-box-shadow: 0 1px 0 #fff inset, 0 1px 0px #dfdfdf;
}
.paging_full_numbers a.paginate_active {
	margin: 0px 3px; padding: 4px 7px; border-radius: 3px; border: 1px solid rgb(211, 211, 211); border-image: none; color: rgb(85, 85, 85); font-size: 11px; cursor: pointer; box-shadow: inset 0px 1px 0px #fff, 0px 1px 0px #dfdfdf; -webkit-box-shadow: 0 1px 0 #fff inset, 0 1px 0px #dfdfdf;
}
.paging_full_numbers a.paginate_button {
	background: linear-gradient(rgb(249, 249, 249) 0%, rgb(238, 238, 238) 100%);
}
.paging_full_numbers a.paginate_button:hover {
	background: linear-gradient(rgb(246, 246, 246) 0%, rgb(232, 232, 232) 100%);
}
.paging_full_numbers a.paginate_active {
	background: rgb(237, 237, 237); border-color: rgb(205, 205, 205); box-shadow: inset 0px 1px 2px #f5f5f5, 0px 1px 0px #ffffff; -webkit-box-shadow: 0 1px 2px #F5F5F5 inset, 0 1px 0 #FFFFFF;
}
.paging_full_numbers a.paginate_button:active {
	background: rgb(237, 237, 237); border-color: rgb(205, 205, 205); box-shadow: inset 0px 1px 2px #f5f5f5, 0px 1px 0px #ffffff; -webkit-box-shadow: 0 1px 2px #F5F5F5 inset, 0 1px 0 #FFFFFF;
}
.paginate_button_disabled {
	background: rgb(234, 234, 234) !important; color: rgb(197, 197, 197) !important; font-weight: normal !important; box-shadow: none !important; -webkit-box-shadow: none;
}
.paginate_button_disabled:active {
	background: rgb(234, 234, 234) !important; color: rgb(197, 197, 197) !important; font-weight: normal !important; box-shadow: none !important; -webkit-box-shadow: none;
}
.paginate_button_disabled:active {
	border-color: rgb(211, 211, 211) !important;
}
.dataTables_processing {
	padding: 14px 0px 2px; border: 1px solid rgb(221, 221, 221); border-image: none; left: 50%; top: 50%; width: 250px; height: 30px; text-align: center; color: rgb(153, 153, 153); font-size: 14px; margin-top: -15px; margin-left: -125px; position: absolute; background-color: white;
}
.dataTable thead tr .sorting {
	background: url("../img/sort.png") no-repeat 97%;
}
.dataTable thead tr .sorting_asc {
	background: url("../img/sort_asc.png") no-repeat right rgb(238, 238, 238);
}
.dataTable thead tr .sorting_desc {
	background: url("../img/sort_desc.png") no-repeat right rgb(238, 238, 238);
}
.dataTable thead tr .sorting_asc_disabled {
	background: url("../img/sort_asc_disabled.png") no-repeat right;
}
.dataTable thead tr .sorting_desc_disabled {
	background: url("../img/sort_desc_disabled.png") no-repeat right;
}
@media not all
{
.dataTable thead tr .sorting {
	background-position: 97%; background-image: url("../img/sort.png");
}
}
table.dataTable th:active {
	
}
.dataTables_scroll {
	clear: both;
}
.dataTables_scrollBody {
	-webkit-overflow-scrolling: touch;
}
.jslider .jslider-bg i {
	background: url("../img/jslider.png") no-repeat 0px 0px;
}
.jslider .jslider-pointer {
	background: url("../img/jslider.png") no-repeat 0px 0px;
}
.jslider {
	top: 0.6em; width: 100%; height: 1em; display: block; position: relative;
}
.jslider table {
	border: 0px currentColor; border-image: none; width: 100%; border-collapse: collapse;
}
.jslider td {
	padding: 0px; border: 0px currentColor; border-image: none; text-align: left; vertical-align: top;
}
.jslider th {
	padding: 0px; border: 0px currentColor; border-image: none; text-align: left; vertical-align: top;
}
.jslider table {
	width: 100%; vertical-align: top;
}
.jslider table tr {
	width: 100%; vertical-align: top;
}
.jslider table tr td {
	width: 100%; vertical-align: top;
}
.jslider .jslider-bg {
	position: relative;
}
.jslider .jslider-bg i {
	top: 0px; height: 5px; font-size: 0px; position: absolute;
}
.jslider .jslider-bg .l {
	background-position: 0px 0px; left: 0px; width: 50%;
}
.jslider .jslider-bg .r {
	background-position: right 0px; left: 50%; width: 50%;
}
.jslider .jslider-bg .v {
	background-position: 0px -20px; left: 20%; top: 0px; width: 60%; height: 5px; position: absolute;
}
.jslider .jslider-pointer {
	background-position: 0px -40px; left: 20%; top: -4px; width: 13px; height: 15px; margin-left: -6px; position: absolute; cursor: pointer;
}
.jslider .jslider-pointer-hover {
	background-position: -20px -40px;
}
.jslider .jslider-pointer-to {
	left: 80%;
}
.jslider .jslider-label {
	padding: 0px 2px; left: 0px; top: -18px; color: rgb(85, 85, 85); font-size: 12px; white-space: nowrap; position: absolute;
}
.jslider .jslider-label-to {
	left: auto; right: 0px;
}
.jslider .jslider-value {
	background: linear-gradient(rgb(248, 248, 248) 0%, rgb(232, 232, 232) 100%); padding: 2px 5px; border-radius: 1px; border: 1px solid rgb(205, 205, 205); border-image: none; left: 20%; top: -25px; line-height: 12px; font-size: 12px; white-space: nowrap; position: absolute; -webkit-border-radius: 1px; -moz-border-radius: 1px; -o-border-radius: 1px;
}
.jslider .jslider-value-to {
	left: 80%;
}
.jslider .jslider-label small {
	top: -0.4em; position: relative;
}
.jslider .jslider-value small {
	top: -0.4em; position: relative;
}
.jslider .jslider-scale {
	top: 9px; position: relative;
}
.jslider .jslider-scale span {
	height: 5px; font-size: 0px; border-left-color: rgb(205, 205, 205); border-left-width: 1px; border-left-style: solid; position: absolute;
}
.jslider .jslider-scale ins {
	left: 0px; top: 5px; color: rgb(85, 85, 85); font-size: 12px; text-decoration: none; position: absolute;
}
.jslider-single .jslider-pointer-to {
	display: none;
}
.jslider-single .jslider-value-to {
	display: none;
}
.jslider-single .jslider-bg .v {
	display: none;
}
.jslider-limitless .jslider-label {
	display: none;
}
.jslider_round_plastic .jslider-bg i {
	background-image: url("../img/jslider.round.plastic.png");
}
.jslider_round_plastic .jslider-pointer {
	background-image: url("../img/jslider.round.plastic.png");
}
.jslider_round_plastic .jslider-pointer {
	top: -7px; width: 18px; height: 18px; margin-left: -8px;
}
.fc {
	text-align: left; direction: ltr;
}
.fc table {
	border-collapse: collapse; border-spacing: 0;
}
html .fc {
	font-size: 1em;
}
.fc table {
	font-size: 1em;
}
.fc td {
	padding: 0px; vertical-align: top;
}
.fc th {
	padding: 0px; vertical-align: top;
}
.fc-header td {
	white-space: nowrap;
}
.fc-header-left {
	width: 25%; text-align: left;
}
.fc-header-center {
	text-align: center;
}
.fc-header-right {
	width: 25%; text-align: right;
}
.fc-header-title {
	vertical-align: top; display: inline-block;
}
.fc-header-title h2 {
	line-height: 27px; font-size: 18px; margin-top: 0px; white-space: nowrap;
}
.fc .fc-header-space {
	padding-left: 10px;
}
.fc-header .fc-button {
	margin-bottom: 1em; vertical-align: top;
}
.fc-header .fc-button {
	margin-right: -1px;
}
.fc-header .fc-corner-right {
	margin-right: 1px;
}
.fc-header .ui-corner-right {
	margin-right: 0px;
}
.fc-header .fc-state-hover {
	z-index: 2;
}
.fc-header .ui-state-hover {
	z-index: 2;
}
.fc-header .fc-state-down {
	z-index: 3;
}
.fc-header .fc-state-active {
	z-index: 4;
}
.fc-header .ui-state-active {
	z-index: 4;
}
.fc-content {
	clear: both;
}
.fc-view {
	width: 100%; overflow: hidden;
}
.fc-widget-header {
	border: 1px solid rgb(205, 205, 205); border-image: none;
}
.fc-widget-content {
	border: 1px solid rgb(205, 205, 205); border-image: none;
}
.fc-state-highlight {
	background: linear-gradient(rgb(252, 252, 252) 0%, rgb(241, 241, 241) 100%);
}
.fc-cell-overlay {
	background: rgb(153, 204, 255); opacity: 0.2;
}
.fc-button {
	background: linear-gradient(rgb(248, 248, 248) 0%, rgb(232, 232, 232) 100%); display: inline-block; position: relative; cursor: pointer;
}
.fc-state-default {
	border-width: 1px 0px; border-style: solid;
}
.fc-button-inner {
	overflow: hidden; float: left; position: relative;
}
.fc-state-default .fc-button-inner {
	border-width: 0px 1px; border-style: solid;
}
.fc-button-content {
	padding: 0px 0.6em; height: 1.9em; line-height: 1.9em; float: left; white-space: nowrap; position: relative;
}
.fc-button-content .fc-icon-wrap {
	top: 50%; float: left; position: relative;
}
.fc-button-content .ui-icon {
	margin-top: -50%; float: left; position: relative;
}
.fc-state-default .fc-button-effect {
	left: 0px; top: 50%; position: absolute;
}
.fc-state-default .fc-button-effect span {
	border-width: 100px 0px 0px 1px; border-style: solid; border-color: rgb(255, 255, 255); left: 0px; top: -100px; width: 500px; height: 100px; position: absolute; opacity: 0.09;
}
.fc-state-default {
	border-style: solid; border-color: rgb(205, 205, 205);
}
.fc-state-default .fc-button-inner {
	border-style: solid; border-color: rgb(205, 205, 205);
}
.fc-state-hover {
	border-color: rgb(205, 205, 205);
}
.fc-state-hover .fc-button-inner {
	border-color: rgb(205, 205, 205);
}
.fc-state-down {
	border-color: rgb(205, 205, 205);
}
.fc-state-down .fc-button-inner {
	border-color: rgb(205, 205, 205);
}
.fc-state-active {
	background: linear-gradient(rgb(252, 252, 252) 0%, rgb(241, 241, 241) 100%); border-color: rgb(205, 205, 205);
}
.fc-state-active .fc-button-inner {
	background: linear-gradient(rgb(252, 252, 252) 0%, rgb(241, 241, 241) 100%); border-color: rgb(205, 205, 205);
}
.fc-state-disabled {
	border-color: rgb(205, 205, 205); color: rgb(85, 85, 85);
}
.fc-state-disabled .fc-button-inner {
	border-color: rgb(205, 205, 205); color: rgb(85, 85, 85);
}
.fc-state-disabled {
	cursor: default;
}
.fc-state-disabled .fc-button-effect {
	display: none;
}
.fc-event {
	border-width: 0px; border-style: solid; font-size: 0.95em; cursor: default;
}
a.fc-event {
	cursor: pointer;
}
.fc-event-draggable {
	cursor: pointer;
}
a.fc-event {
	text-decoration: none;
}
.fc-rtl .fc-event {
	text-align: right;
}
.fc-event-skin {
	border-color: rgb(205, 205, 205); color: rgb(85, 85, 85); background-color: rgb(215, 215, 215);
}
.fc-event-inner {
	border-width: 0px; border-style: solid; width: 100%; height: 100%; overflow: hidden; position: relative;
}
.fc-event-title {
	padding: 0px 5px;
}
.fc .ui-resizable-handle {
	line-height: 50%; overflow: hidden; font-size: 300%; display: block; position: absolute; z-index: 99999;
}
.fc-event-hori {
	border-width: 1px 0px; margin-bottom: 1px;
}
.fc-event-hori .ui-resizable-e {
	top: 0px !important; width: 7px !important; height: 100% !important; right: -3px !important; cursor: e-resize;
}
.fc-event-hori .ui-resizable-w {
	left: -3px !important; top: 0px !important; width: 7px !important; height: 100% !important; cursor: w-resize;
}
.fc-event-hori .ui-resizable-handle {
	_padding-bottom: 14px;
}
.fc-corner-left {
	margin-left: 1px;
}
.fc-corner-left .fc-button-inner {
	margin-left: -1px;
}
.fc-corner-left .fc-event-inner {
	margin-left: -1px;
}
.fc-corner-right {
	margin-right: 1px;
}
.fc-corner-right .fc-button-inner {
	margin-right: -1px;
}
.fc-corner-right .fc-event-inner {
	margin-right: -1px;
}
.fc-corner-top {
	margin-top: 1px;
}
.fc-corner-top .fc-event-inner {
	margin-top: -1px;
}
.fc-corner-bottom {
	margin-bottom: 1px;
}
.fc-corner-bottom .fc-event-inner {
	margin-bottom: -1px;
}
.fc-corner-left .fc-event-inner {
	border-left-width: 1px;
}
.fc-corner-right .fc-event-inner {
	border-right-width: 1px;
}
.fc-corner-top .fc-event-inner {
	border-top-width: 1px;
}
.fc-corner-bottom .fc-event-inner {
	border-bottom-width: 1px;
}
table.fc-border-separate {
	border-collapse: separate;
}
.fc-border-separate th {
	border-width: 1px 0px 0px 1px;
}
.fc-border-separate td {
	border-width: 1px 0px 0px 1px;
}
.fc-border-separate th.fc-last {
	border-right-width: 1px;
}
.fc-border-separate td.fc-last {
	border-right-width: 1px;
}
.fc-border-separate tr.fc-last th {
	border-bottom-width: 1px;
}
.fc-border-separate tr.fc-last td {
	border-bottom-width: 1px;
}
.fc-border-separate tbody tr.fc-first td {
	border-top-width: 0px;
}
.fc-border-separate tbody tr.fc-first th {
	border-top-width: 0px;
}
.fc-grid th {
	text-align: center;
}
.fc-grid .fc-day-number {
	padding: 0px 2px; float: right;
}
.fc-grid .fc-other-month .fc-day-number {
	opacity: 0.3;
}
.fc-grid .fc-day-content {
	padding: 2px 2px 1px; clear: both;
}
.fc-grid .fc-event-time {
	color: rgb(207, 26, 26); padding-left: 5px;
}
.fc-rtl .fc-grid .fc-day-number {
	float: left;
}
.fc-rtl .fc-grid .fc-event-time {
	float: right;
}
.fc-agenda table {
	border-collapse: separate;
}
.fc-agenda-days th {
	text-align: center;
}
.fc-agenda .fc-agenda-axis {
	padding: 0px 4px; width: 50px; text-align: right; font-weight: normal; vertical-align: middle; white-space: nowrap;
}
.fc-agenda .fc-day-content {
	padding: 2px 2px 1px;
}
.fc-agenda-days .fc-agenda-axis {
	border-right-width: 1px;
}
.fc-agenda-days .fc-col0 {
	border-left-width: 0px;
}
.fc-agenda-allday th {
	border-width: 0px 1px;
}
.fc-agenda-allday .fc-day-content {
	min-height: 34px; _height: 34px;
}
.fc-agenda-divider-inner {
	height: 2px; overflow: hidden;
}
.fc-widget-header .fc-agenda-divider-inner {
	background: rgb(238, 238, 238);
}
.fc-agenda-slots th {
	border-width: 1px 1px 0px;
}
.fc-agenda-slots td {
	background: none; border-width: 1px 0px 0px;
}
.fc-agenda-slots td div {
	height: 20px;
}
.fc-agenda-slots tr.fc-slot0 th {
	border-top-width: 0px;
}
.fc-agenda-slots tr.fc-slot0 td {
	border-top-width: 0px;
}
.fc-agenda-slots tr.fc-minor th {
	border-top-style: dotted;
}
.fc-agenda-slots tr.fc-minor td {
	border-top-style: dotted;
}
.fc-agenda-slots tr.fc-minor th.ui-widget-header {
	
}
.fc-event-vert {
	border-width: 0px 1px;
}
.fc-event-vert .fc-event-head {
	width: 100%; overflow: hidden; position: relative; z-index: 2;
}
.fc-event-vert .fc-event-content {
	width: 100%; overflow: hidden; position: relative; z-index: 2;
}
.fc-event-vert .fc-event-time {
	font-size: 10px; white-space: nowrap;
}
.fc-event-vert .fc-event-bg {
	background: rgb(255, 255, 255); left: 0px; top: 0px; width: 100%; height: 100%; position: absolute; z-index: 1; opacity: 0.3;
}
.fc .ui-draggable-dragging .fc-event-bg {
	
}
.fc-select-helper .fc-event-bg {
	
}
.fc-event-vert .ui-resizable-s {
	width: 100% !important; height: 8px !important; text-align: center; bottom: 0px !important; line-height: 8px !important; overflow: hidden !important; font-family: monospace; font-size: 11px !important; cursor: s-resize;
}
.fc-agenda .ui-resizable-resizing {
	_overflow: hidden;
}
#bolt-containers {
	width: 100%; position: relative;
}
.bolt-img-grid {
	background: rgb(236, 237, 237); margin: 0px 8px; transition:top 1s, left 1s; width: 188px; font-size: 12px; float: left; min-height: 100px; box-shadow: 0px 1px 3px rgba(34,25,25,0.4); -webkit-box-shadow: 0 1px 3px rgba(34, 25, 25, 0.4); -moz-box-shadow: 0 1px 3px rgba(34, 25, 25, 0.4); -webkit-transition: top 1s ease, left 1s ease; -moz-transition: top 1s ease, left 1s ease; -o-transition: top 1s ease, left 1s ease;
}
.bolt-img-grid h5 {
	margin: 10px 0px; padding: 0px 10px 10px; border-bottom-color: rgb(205, 205, 205); border-bottom-width: 1px; border-bottom-style: solid; display: block;
}
.bolt-img-grid p {
	padding: 0px 10px;
}
.bolt-img-grid .meta {
	text-align: right; color: rgb(119, 119, 119); padding-right: 10px; padding-bottom: 10px; font-size: 11px; font-style: italic;
}
.bolt-img-grid .imgholder img {
	background: rgb(204, 204, 204); display: block; max-width: 100%;
}
@media screen and (max-width:1040px)
{
body {
	overflow: auto;
}
}
.view {
	width: 100%; height: 100%; text-align: center; overflow: hidden; position: relative; cursor: default;
}
.view .mask {
	left: 0px; top: 0px; width: 100%; height: 100%; position: absolute;
}
.view .content {
	left: 0px; top: 0px; width: 100%; height: 100%; position: absolute;
}
.view img {
	display: block; position: relative;
}
.view a.info {
	background: url("/img/link.png") no-repeat center; width: 20px; height: 20px; text-indent: -9999px; text-decoration: none; display: inline-block;
}
a {
	transition:color 0.3s ease-out; -webkit-transition: color 0.3s ease-out; -moz-transition: color 0.3s ease-out; -o-transition: color 0.3s ease-out;
}
.bolt-effect-first img {
	opacity: 1; -moz-transition: all 0.3s ease-in;
}
.bolt-effect-first .mask {
	border: 100px solid rgba(0, 0, 0, 0.3); transition:0.4s cubic-bezier(0.94, 0.85, 0.1, 0.62); border-image: none; visibility: visible; cursor: pointer; box-sizing: border-box; opacity: 1; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -webkit-transition: all 0.4s cubic-bezier(0.940, 0.850, 0.100, 0.620); -moz-transition: all 0.4s cubic-bezier(0.940, 0.850, 0.100, 0.620); -o-transition: all 0.4s cubic-bezier(0.940, 0.850, 0.100, 0.620);
}
.bolt-effect-first:hover .mask {
	border: 0px double rgba(0, 0, 0, 0.7); border-image: none; visibility: hidden; opacity: 0;
}
.bolt-effect-first:hover img {
	opacity: 1;
}
#bolt-container {
	margin: 5px 0px; width: 100%;
}
.bolt-img-element {
	background: rgb(236, 237, 237); margin: 5px; width: 153px; height: 103px; overflow: hidden; float: left; position: relative; box-shadow: 0px 1px 3px rgba(34,25,25,0.4);
}
.bolt-thumbnail {
	padding: 15px; border-radius: 2px; line-height: 1; display: block;
}
.bolt-img-element * {
	margin: 0px;
}
#infscr-loading {
	background: hsla(0, 0%, 100%, 0.9); padding: 20px; border-radius: 10px; left: 42%; text-align: center; bottom: 30px; color: rgb(34, 34, 34); font-size: 15px; font-weight: bold; position: fixed; z-index: 100; -webkit-border-radius: 10px; -moz-border-radius: 10px;
}
.clearfix::before {
	display: table; content: "";
}
.clearfix::after {
	display: table; content: "";
}
.clearfix::after {
	clear: both;
}
.clearfix {
	-ms-zoom: 1;
}
.isotope-item {
	z-index: 2;
}
.isotope-item.isotope-hidden {
	z-index: 1; pointer-events: none;
}
.isotope {
	transition-duration: 0.8s; -webkit-transition-duration: 0.8s; -moz-transition-duration: 0.8s; -o-transition-duration: 0.8s;
}
.isotope .isotope-item {
	transition-duration: 0.8s; -webkit-transition-duration: 0.8s; -moz-transition-duration: 0.8s; -o-transition-duration: 0.8s;
}
.isotope {
	transition-property: height, width; -webkit-transition-property: height, width; -moz-transition-property: height, width; -o-transition-property: height, width;
}
.isotope .isotope-item {
	transition-property: transform, opacity; -webkit-transition-property: -webkit-transform, opacity; -moz-transition-property: -moz-transform, opacity; -o-transition-property: top, left, opacity;
}
.no-transition.isotope {
	transition-duration: 0s; -webkit-transition-duration: 0s; -moz-transition-duration: 0s; -o-transition-duration: 0s;
}
.no-transition.isotope .isotope-item {
	transition-duration: 0s; -webkit-transition-duration: 0s; -moz-transition-duration: 0s; -o-transition-duration: 0s;
}
.isotope .no-transition.isotope-item {
	transition-duration: 0s; -webkit-transition-duration: 0s; -moz-transition-duration: 0s; -o-transition-duration: 0s;
}
.infinite-scrolling.isotope {
	transition:none; -webkit-transition: none; -moz-transition: none; -o-transition: none;
}
.bolt-view {
	text-align: center; overflow: hidden; float: left; position: relative; cursor: default; box-shadow: 0px 1px 3px rgba(34,25,25,0.4);
}
.bolt-view .mask {
	left: 0px; top: 0px; width: 100%; height: 100%; position: absolute;
}
.bolt-view .content {
	left: 0px; top: 0px; width: 100%; height: 100%; position: absolute;
}
.bolt-view img {
	display: block; position: relative;
}
.bolt-view a.info {
	background: url("../img/link.png") no-repeat center; width: 20px; height: 20px; text-indent: -9999px; text-decoration: none; display: inline-block;
}
a {
	transition:color 0.3s ease-out; -webkit-transition: color 0.3s ease-out; -moz-transition: color 0.3s ease-out; -o-transition: color 0.3s ease-out;
}
.bolt-effect .mask {
	border: 0px solid rgba(0, 0, 0, 0.7); transition:0.4s ease-in-out; border-image: none; overflow: visible; box-sizing: border-box; opacity: 0; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -webkit-transition: all 0.4s ease-in-out; -moz-transition: all 0.4s ease-in-out; -o-transition: all 0.4s ease-in-out;
}
.bolt-effect a.info {
	transition:transform 0.2s ease-in 0.1s, opacity 0.1s ease-in-out; left: -18px; top: -48px; position: relative; opacity: 0; transform: scale(0); -webkit-transition: -webkit-transform 0.2s 0.1s ease-in, opacity 0.1s ease-in-out; -moz-transition: -moz-transform 0.2s 0.1s ease-in, opacity 0.1s ease-in-out; -o-transition: -o-transform 0.2s 0.1s ease-in, opacity 0.1s ease-in-out; -webkit-transform: scale(0, 0); -moz-transform: scale(0, 0); -o-transform: scale(0, 0);
}
.bolt-effect:hover .mask {
	border: 100px solid rgba(0, 0, 0, 0.7); border-image: none; opacity: 1;
}
.bolt-effect:hover a.info {
	opacity: 1; transform: scale(1); transition-delay: 0.3s; -webkit-transform: scale(1, 1); -moz-transform: scale(1, 1); -o-transform: scale(1, 1); -moz-transition-delay: 0.3s; -webkit-transition-delay: 0.3s; -o-transition-delay: 0.3s;
}
.am-wrapper {
	overflow: hidden; float: left; position: relative;
}
.am-wrapper img {
	width: auto !important; position: absolute; max-width: 1024px;
}
.bolt-thumbs-2 a {
	background: rgb(255, 255, 255); margin: 12px; float: left; position: relative;
}
.bolt-thumbs-2 a {
	display: block; position: relative;
}
.bolt-thumbs a img {
	display: block; position: relative;
}
.bolt--thumbs-2 a {
	overflow: hidden;
}
.bolt-thumbs-2 a div {
	background: rgba(75, 75, 75, 0.7); width: 100%; height: 100%; position: absolute;
}
.bolt-thumbs-2 a div.da-animate {
	transition:0.3s ease-in-out; -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out;
}
.da-slideFromTop {
	left: 0px; top: -100%;
}
.da-slideFromBottom {
	left: 0px; top: 100%;
}
.da-slideFromLeft {
	left: -100%; top: 0px;
}
.da-slideFromRight {
	left: 100%; top: 0px;
}
.da-slideTop {
	top: 0px;
}
.da-slideLeft {
	left: 0px;
}
.bolt-thumbs-2 a div span {
	margin: 40px 20px 20px; padding: 10px 0px; color: rgba(255, 255, 255, 0.9); text-transform: uppercase; font-weight: normal; border-bottom-color: rgba(255, 255, 255, 0.5); border-bottom-width: 1px; border-bottom-style: solid; display: block; box-shadow: 0px 1px 0px rgba(0,0,0,0.1), 0px -10px 0px rgba(255,255,255,0.3); text-shadow: 1px 1px 1px rgba(0,0,0,0.2);
}
.bolt-thumbs-2 a div {
	transition:0.3s ease-in-out; left: -100%; top: 0px; -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out;
}
.bolt-thumbs-2 a:hover div {
	left: 0px;
}
.bolt-reset {
	width: 100%;
}
ul.bolt-reset {
	text-align: center;
}
ul.bolt-reset {
	list-style: none; margin: 5px 0px 0px 4px; padding: 0px; display: block;
}
ul.bolt-reset li {
	list-style: none; margin: 5px 0px 0px 4px; padding: 0px; display: block;
}
.gallery ul li {
	width: 150px; height: 150px; float: left; position: relative;
}
.holder {
	margin: -80px 0px 0px -80px; left: 0px; top: 0px; position: absolute;
}
a img {
	border: currentColor; border-image: none;
}
.bolt-embed-video iframe {
	height: 300px;
}
#player {
	width: 100%; text-align: center; display: block;
}
.lists {
	list-style: none; margin: 5px; border-radius: 10px; border: 1px solid rgb(238, 238, 238); border-image: none; width: 145px; height: 130px; overflow: hidden; float: left;
}
.video-description {
	padding: 5px 0px; height: 25px; text-align: center; background-color: rgb(227, 227, 227);
}
#clips {
	background: url("../img/flowplayer/h150.png") repeat-x 0px 0px rgb(255, 255, 255); width: 100%; height: 67px; margin-top: 7px;
}
.playlist {
	height: 285px !important; overflow: hidden; position: relative;
}
.playlist .clips {
	height: 20000em; position: absolute;
}
.playlist {
	margin: 20px; width: 230px;
}
.clips {
	margin: 20px; width: 230px;
}
.clips a {
	background: url("../img/flowplayer/h80.png") rgb(254, 254, 255); padding: 12px 15px; border: 1px outset rgb(204, 204, 204); border-image: none; width: 195px; height: 46px; color: rgb(0, 0, 0); letter-spacing: -1px; font-family: Overlock SC; font-size: 12px; text-decoration: none; display: block; cursor: pointer;
}
.clips a.first {
	border-top-width: 1px;
}
.clips a.playing {
	background: url("../img/flowplayer/light.png") no-repeat 0px -69px; border: 0px currentColor; border-image: none; width: 225px;
}
.clips a.paused {
	background: url("../img/flowplayer/light.png") no-repeat 0px -69px; border: 0px currentColor; border-image: none; width: 225px;
}
.clips a.progress {
	background: url("../img/flowplayer/light.png") no-repeat 0px -69px; border: 0px currentColor; border-image: none; width: 225px;
}
.clips a.progress {
	opacity: 0.6;
}
.clips a.paused {
	background-position: 0px 0px;
}
.clips a span {
	color: rgb(102, 102, 102); font-size: 11px; display: block;
}
.clips a em {
	color: rgb(255, 0, 0); font-style: normal;
}
.clips a:hover {
	background-color: rgb(230, 230, 230);
}
.clips a.playing:hover {
	background-color: transparent !important;
}
.clips a.paused:hover {
	background-color: transparent !important;
}
.clips a.progress:hover {
	background-color: transparent !important;
}
.petrol.clips a {
	border: 1px outset rgb(225, 225, 225); border-image: none; color: rgb(255, 255, 255); background-color: rgb(204, 204, 204);
}
.petrol.clips a.playing {
	background: url("../img/flowplayer/light.png") no-repeat 0px -69px; border: 0px currentColor; border-image: none;
}
.petrol.clips a.paused {
	background: url("../img/flowplayer/light.png") no-repeat 0px -69px; border: 0px currentColor; border-image: none;
}
.petrol.clips a.progress {
	background: url("../img/flowplayer/light.png") no-repeat 0px -69px; border: 0px currentColor; border-image: none;
}
.petrol.clips a.paused {
	background-position: 0px 0px;
}
.petrol.clips a span {
	color: rgb(170, 170, 170);
}
.petrol.clips a em {
	color: rgb(240, 240, 240); font-weight: bold;
}
.petrol.clips a:hover {
	background-color: rgb(224, 224, 224);
}
.petrol.clips a.playing:hover {
	background-color: transparent !important;
}
.petrol.clips a.paused:hover {
	background-color: transparent !important;
}
.petrol.clips a.progress:hover {
	background-color: transparent !important;
}
.low.clips a {
	height: 31px;
}
.low.clips a.playing {
	background-position: 0px -55px; background-image: url("../img/flowplayer/light_small.png");
}
.low.clips a.paused {
	background-position: 0px -55px; background-image: url("../img/flowplayer/light_small.png");
}
.low.clips a.progress {
	background-position: 0px -55px; background-image: url("../img/flowplayer/light_small.png");
}
.low.clips a.paused {
	background-position: 0px 0px;
}
a.go {
	background: url("../img/flowplayer/up.png") no-repeat; margin: 5px 0px 5px 105px; width: 18px; height: 18px; display: block; cursor: pointer;
}
a.go:hover {
	background-position: 0px -18px;
}
a.down.go:hover {
	background-position: 0px -18px;
}
a.down.go {
	background-image: url("../img/flowplayer/down.png");
}
.petrol a.go {
	background-image: url("../img/flowplayer/up_dark.png");
}
.petrol a.down.go {
	background-image: url("../img/flowplayer/down_dark.png");
}
a.disabled.go {
	visibility: hidden;
}
#player_wrap {
	background: rgb(242, 242, 242); border: 2px solid rgb(255, 255, 255); border-image: none; width: 100%; margin-bottom: 15px; -moz-outline-radius: 4px;
}
#colorbox {
	left: 0px; top: 0px; overflow: hidden; position: absolute; z-index: 99999;
}
#cboxOverlay {
	left: 0px; top: 0px; overflow: hidden; position: absolute; z-index: 99999;
}
#cboxWrapper {
	left: 0px; top: 0px; overflow: hidden; position: absolute; z-index: 99999;
}
#cboxOverlay {
	width: 100%; height: 100%; position: fixed;
}
#cboxMiddleLeft {
	clear: left;
}
#cboxBottomLeft {
	clear: left;
}
#cboxContent {
	position: relative;
}
#cboxLoadedContent {
	overflow: auto;
}
#cboxTitle {
	margin: 0px;
}
#cboxLoadingOverlay {
	left: 0px; top: 0px; width: 100%; height: 100%; position: absolute;
}
#cboxLoadingGraphic {
	left: 0px; top: 0px; width: 100%; height: 100%; position: absolute;
}
#cboxPrevious {
	cursor: pointer;
}
#cboxNext {
	cursor: pointer;
}
#cboxClose {
	cursor: pointer;
}
#cboxSlideshow {
	cursor: pointer;
}
.cboxPhoto {
	margin: auto; padding: 0px; border: 0px currentColor; border-image: none; float: left; display: block;
}
.cboxIframe {
	border: 0px currentColor; border-image: none; width: 100%; height: 100%; display: block;
}
#cboxOverlay {
	background: rgb(0, 0, 0);
}
#colorbox {
	font: 12px/normal Tahoma, Arial, sans-serif; font-size-adjust: none; font-stretch: normal;
}
#cboxTopLeft {
	background: url("../img/colorbox/controls.png") no-repeat -100px 0px; width: 21px; height: 21px;
}
#cboxTopRight {
	background: url("../img/colorbox/controls.png") no-repeat -129px 0px; width: 21px; height: 21px;
}
#cboxBottomLeft {
	background: url("../img/colorbox/controls.png") no-repeat -100px -29px; width: 21px; height: 21px;
}
#cboxBottomRight {
	background: url("../img/colorbox/controls.png") no-repeat -129px -29px; width: 21px; height: 21px;
}
#cboxMiddleLeft {
	background: url("../img/colorbox/controls.png") repeat-y left top; width: 21px;
}
#cboxMiddleRight {
	background: url("../img/colorbox/controls.png") repeat-y right top; width: 21px;
}
#cboxTopCenter {
	background: url("../img/colorbox/border.png") repeat-x 0px 0px; height: 21px;
}
#cboxBottomCenter {
	background: url("../img/colorbox/border.png") repeat-x 0px -29px; height: 21px;
}
#cboxContent {
	background: rgb(255, 255, 255); overflow: hidden;
}
.cboxIframe {
	background: rgb(255, 255, 255);
}
#cboxError {
	padding: 50px; border: 1px solid rgb(204, 204, 204); border-image: none;
}
#cboxLoadedContent {
	margin-bottom: 28px;
}
#cboxTitle {
	left: 0px; width: 100%; text-align: center; bottom: 4px; color: rgb(148, 148, 148); position: absolute;
}
#cboxCurrent {
	left: 58px; bottom: 4px; color: rgb(148, 148, 148); position: absolute;
}
#cboxSlideshow {
	right: 30px; bottom: 4px; color: rgb(0, 146, 239); position: absolute;
}
#cboxPrevious {
	background: url("../img/colorbox/controls.png") no-repeat -75px 0px; left: 0px; width: 25px; height: 25px; bottom: 0px; text-indent: -9999px; position: absolute;
}
#cboxPrevious:hover {
	background-position: -75px -25px;
}
#cboxNext {
	background: url("../img/colorbox/controls.png") no-repeat -50px 0px; left: 27px; width: 25px; height: 25px; bottom: 0px; text-indent: -9999px; position: absolute;
}
#cboxNext:hover {
	background-position: -50px -25px;
}
#cboxLoadingOverlay {
	background: url("../img/colorbox/loading_background.png") no-repeat center;
}
#cboxLoadingGraphic {
	background: url("../img/colorbox/loading.gif") no-repeat center;
}
#cboxClose {
	background: url("../img/colorbox/controls.png") no-repeat -25px 0px; width: 25px; height: 25px; right: 0px; bottom: 0px; text-indent: -9999px; position: absolute;
}
#cboxClose:hover {
	background-position: -25px -25px;
}
#joyRideTipContent {
	display: none;
}
.joyride-tip-guide {
	border-radius: 5px; left: 0px; top: 0px; color: rgb(255, 255, 255); display: none; position: absolute; z-index: 101; opacity: 0.8; background-color: rgb(0, 0, 0); -webkit-border-radius: 5px; -moz-border-radius: 5px;
}
.joyride-content-wrapper {
	padding: 10px 10px 15px 15px;
}
@media only screen and (max-width:767px)
{
.joyride-tip-guide {
	border-radius: 0px; left: 2.5% !important; width: 95% !important; -webkit-border-radius: 0; -moz-border-radius: 0;
}
.joyride-tip-guide-wrapper {
	width: 100%;
}
}
.joyride-tip-guide span.joyride-nub {
	border: 14px solid currentColor; border-image: none; left: 22px; width: 0px; height: 0px; display: block; position: absolute; opacity: 0.99;
}
.joyride-tip-guide span.top.joyride-nub {
	top: -28px; border-top-color: transparent !important; border-right-color: transparent !important; border-bottom-color: rgb(0, 0, 0); border-left-color: transparent !important;
}
.joyride-tip-guide span.bottom.joyride-nub {
	border-color: rgb(0, 0, 0) transparent transparent !important; bottom: -28px;
}
.joyride-tip-guide span.right.joyride-nub {
	border-color: transparent transparent transparent rgb(0, 0, 0) !important; left: auto; top: 22px; right: -28px;
}
.joyride-tip-guide span.left.joyride-nub {
	border-color: transparent rgb(0, 0, 0) transparent transparent !important; left: -28px; top: 22px; right: auto;
}
.joyride-tip-guide h1 {
	line-height: 1.25; font-size: 14px; font-weight: normal;
}
.joyride-tip-guide h2 {
	line-height: 1.25; font-size: 14px; font-weight: normal;
}
.joyride-tip-guide h3 {
	line-height: 1.25; font-size: 14px; font-weight: normal;
}
.joyride-tip-guide h4 {
	line-height: 1.25; font-size: 14px; font-weight: normal;
}
.joyride-tip-guide h5 {
	line-height: 1.25; font-size: 14px; font-weight: normal;
}
.joyride-tip-guide h6 {
	line-height: 1.25; font-size: 14px; font-weight: normal;
}
.joyride-tip-guide p {
	margin: 10px 0px; line-height: 18px;
}
.joyride-tip-guide a {
	color: rgb(255, 255, 255); text-decoration: none; border-bottom-color: rgba(255, 255, 255, 0.6); border-bottom-width: 1px; border-bottom-style: dotted;
}
.joyride-tip-guide a:hover {
	color: rgb(170, 170, 170); border-bottom-color: currentColor; border-bottom-width: medium; border-bottom-style: none;
}
.joyride-tip-guide .joyride-next-tip {
	background: -ms-linear-gradient(rgb(0, 99, 255) 0%, rgb(0, 85, 214) 100%); padding: 6px 18px 4px; border-radius: 2px; border: 1px solid rgb(0, 60, 180); border-image: none; width: auto; color: rgb(255, 255, 255); font-size: 13px; text-decoration: none; box-shadow: inset 0px 1px 0px rgba(255,255,255,0.3); text-shadow: 0px -1px 0px rgba(0,0,0,0.5); -webkit-border-radius: 2px; -moz-border-radius: 2px; -webkit-box-shadow: 0px 1px 0px rgba(255, 255, 255, 0.3) inset; -moz-box-shadow: 0px 1px 0px rgba(255, 255, 255, 0.3) inset;
}
.joyride-next-tip:hover {
	background: -ms-linear-gradient(rgb(43, 128, 255) 0%, rgb(29, 102, 211) 100%); border: 1px solid rgb(0, 60, 180) !important; border-image: none !important; color: rgb(255, 255, 255) !important;
}
.joyride-timer-indicator-wrap {
	border: 1px solid rgba(255, 255, 255, 0.1); border-image: none; width: 50px; height: 3px; right: 17px; bottom: 16px; position: absolute;
}
.joyride-timer-indicator {
	background: rgba(255, 255, 255, 0.25); width: 0px; height: inherit; display: block;
}
.joyride-close-tip {
	top: 10px; right: 10px; color: rgb(85, 85, 85) !important; font-size: 10px; font-weight: bold; text-decoration: none; border-bottom-color: currentColor !important; border-bottom-width: medium !important; border-bottom-style: none !important; position: absolute;
}
.joyride-close-tip:hover {
	color: rgb(170, 170, 170) !important;
}
.joyride-modal-bg {
	background: rgba(0, 0, 0, 0.5); left: 0px; top: 0px; width: 100%; height: 100%; display: none; position: fixed; z-index: 100; cursor: pointer; opacity: 0.5;
}
