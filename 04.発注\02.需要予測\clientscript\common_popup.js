var default_style =
	"dialogTop    : 100;" +
	"dialogLeft   : 250;" +
	"dialogHeight : 100px;" +
	"dialogWidth  : 100px;" +
	"help         : no;" +
	"resizable    : no;" +
	"scroll       : no;" +
	"status       : no;";
// 22022/03/10 LONG.TT(S)
var default_chromeOrEdgeStyle = "Top=100px,Left=250,Height=100px,Width=100px,resizable=no,scroll=no,status=no";
// 2022/03/10 LONG.TT(E)
//Open Common Popup--------------------------------------------------------------------------------------
function open_common_popup(_popupid, _return, _parameter) {

	if (_popupid == null) {
		return false;
	}
	// 2022/03/10 LONG.TT(S)
	if (newWin && !newWin.closed){
		newWin.focus();
		return;
	}
	// 2022/03/10 LONG.TT(E)
	var url = "./pop?popup_id=" + _popupid;

	if (_parameter != null) {
		for (var i = 0; i < _parameter.length; i++) {
			url += "&param" + [i] + "=" + _parameter[i];
		}
	}

	args = new Array();
	if (_return != null) {
		for (var j = 0; j < _return.length; j++) {
			args[j] = eval("document." + _return[j]);
		}
	}

	// 2020/03/31 HOAN.ND マルチブラウザ対応(S)
	if (isChrome() || isEdgeChromium()) {

//2021/03/17 A.Fujiwara ビバ課題redmine#1213対応(S)
// 2021/03/05 R.Morimoto Redmine#527 対応 (S)
//		var model = window.open(url, "", default_style, null);
//		var model = window.open(url, "popup", default_style, null);
// 2021/03/05 R.Morimoto Redmine#527 対応 (E)
		//2022/03/10 LONG.TT マルチブラウザ対応 (S)
		//var model = window.open(url, "popup", default_style);
		var model = window.open(encodeURI(url), "popup", default_chromeOrEdgeStyle);
		//2022/03/10 LONG.TT マルチブラウザ対応 (E)
		model.dialogArguments = args;
		//2022/03/10 LONG.TT マルチブラウザ対応 (S)
		model.focus();
		newWin = model;
		//2022/03/10 LONG.TT マルチブラウザ対応 (E)

	} else {
//		window.showModalDialog(url,args,default_style);
		window.showModalDialog(encodeURI(url),args,default_style);
//2021/03/17 A.Fujiwara ビバ課題redmine#1213対応(E)

	}
	// 2020/03/31 HOAN.ND マルチブラウザ対応(E)
}

//--------------------------------------------------------------------------------------------------------



