.clearfix {
	
}
.clearfix::before {
	display: table; content: "";
}
.clearfix::after {
	display: table; content: "";
}
.clearfix::after {
	clear: both;
}
.hide-text {
	font: 0px/0 a; border: 0px currentColor; border-image: none; color: transparent; font-size-adjust: none; font-stretch: normal; text-shadow: none; background-color: transparent;
}
.input-block-level {
	width: 100%; display: block; min-height: 28px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
.hidden {
	display: none; visibility: hidden;
}
.visible-phone {
	display: none !important;
}
.visible-tablet {
	display: none !important;
}
.hidden-desktop {
	display: none !important;
}
@media all and (max-width:767px)
{
.visible-phone {
	display: inherit !important;
}
.hidden-phone {
	display: none !important;
}
.hidden-desktop {
	display: inherit !important;
}
.visible-desktop {
	display: none !important;
}
}
@media all and (max-width:979px) and (min-width:768px)
{
.visible-tablet {
	display: inherit !important;
}
.hidden-tablet {
	display: none !important;
}
.hidden-desktop {
	display: inherit !important;
}
.visible-desktop {
	display: none !important;
}
}
@media all and (max-width:480px)
{
.nav-collapse {
	-webkit-transform: translate3d(0, 0, 0);
}
.page-header h1 small {
	line-height: 18px; display: block;
}
input[type='checkbox'] {
	border: 1px solid rgb(204, 204, 204); border-image: none;
}
input[type='radio'] {
	border: 1px solid rgb(204, 204, 204); border-image: none;
}
.form-horizontal .control-group > label {
	width: auto; text-align: left; padding-top: 0px; float: none;
}
.form-horizontal .controls {
	margin-left: 0px;
}
.form-horizontal .control-list {
	padding-top: 0px;
}
.form-horizontal .form-actions {
	padding-right: 10px; padding-left: 10px;
}
.modal {
	margin: 0px; left: 10px; top: 10px; width: auto; right: 10px; position: absolute;
}
.in.fade.modal {
	top: auto;
}
.modal-header .close {
	margin: -10px; padding: 10px;
}
.carousel-caption {
	position: static;
}
}
@media all and (max-width:767px)
{
body {
	padding-right: 20px; padding-left: 20px;
}
.navbar-fixed-top {
	margin-right: -20px; margin-left: -20px;
}
.navbar-fixed-bottom {
	margin-right: -20px; margin-left: -20px;
}
.container-fluid {
	padding: 0px;
}
.dl-horizontal dt {
	width: auto; text-align: left; clear: none; float: none;
}
.dl-horizontal dd {
	margin-left: 0px;
}
.container {
	width: auto;
}
.row-fluid {
	width: 100%;
}
.row {
	margin-left: 0px;
}
.thumbnails {
	margin-left: 0px;
}
[class*='span'] {
	width: auto; margin-left: 0px; float: none; display: block;
}
.row-fluid [class*='span'] {
	width: auto; margin-left: 0px; float: none; display: block;
}
.input-large {
	width: 100%; display: block; min-height: 28px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
.input-xlarge {
	width: 100%; display: block; min-height: 28px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
.input-xxlarge {
	width: 100%; display: block; min-height: 28px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
input[class*='span'] {
	width: 100%; display: block; min-height: 28px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
select[class*='span'] {
	width: 100%; display: block; min-height: 28px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
textarea[class*='span'] {
	width: 100%; display: block; min-height: 28px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
.uneditable-input {
	width: 100%; display: block; min-height: 28px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
.input-prepend input {
	width: auto; display: inline-block;
}
.input-append input {
	width: auto; display: inline-block;
}
.input-prepend input[class*='span'] {
	width: auto; display: inline-block;
}
.input-append input[class*='span'] {
	width: auto; display: inline-block;
}
}
@media all and (max-width:979px) and (min-width:768px)
{
.row {
	margin-left: -20px;
}
.row::before {
	display: table; content: "";
}
.row::after {
	display: table; content: "";
}
.row::after {
	clear: both;
}
[class*='span'] {
	margin-left: 20px; float: left;
}
.container {
	width: 724px;
}
.navbar-fixed-top .container {
	width: 724px;
}
.navbar-fixed-bottom .container {
	width: 724px;
}
.span12 {
	width: 724px;
}
.span11 {
	width: 662px;
}
.span10 {
	width: 600px;
}
.span9 {
	width: 538px;
}
.span8 {
	width: 476px;
}
.span7 {
	width: 414px;
}
.span6 {
	width: 352px;
}
.span5 {
	width: 290px;
}
.span4 {
	width: 228px;
}
.span3 {
	width: 166px;
}
.span2 {
	width: 104px;
}
.span1 {
	width: 42px;
}
.offset12 {
	margin-left: 764px;
}
.offset11 {
	margin-left: 702px;
}
.offset10 {
	margin-left: 640px;
}
.offset9 {
	margin-left: 578px;
}
.offset8 {
	margin-left: 516px;
}
.offset7 {
	margin-left: 454px;
}
.offset6 {
	margin-left: 392px;
}
.offset5 {
	margin-left: 330px;
}
.offset4 {
	margin-left: 268px;
}
.offset3 {
	margin-left: 206px;
}
.offset2 {
	margin-left: 144px;
}
.offset1 {
	margin-left: 82px;
}
.row-fluid {
	width: 100%;
}
.row-fluid::before {
	display: table; content: "";
}
.row-fluid::after {
	display: table; content: "";
}
.row-fluid::after {
	clear: both;
}
.row-fluid [class*='span'] {
	width: 100%; margin-left: 2.76%; float: left; display: block; min-height: 28px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
.row-fluid [class*='span']:first-child {
	margin-left: 0px;
}
.row-fluid .span12 {
	width: 99.99%;
}
.row-fluid .span11 {
	width: 91.43%;
}
.row-fluid .span10 {
	width: 82.87%;
}
.row-fluid .span9 {
	width: 74.3%;
}
.row-fluid .span8 {
	width: 65.74%;
}
.row-fluid .span7 {
	width: 57.18%;
}
.row-fluid .span6 {
	width: 48.61%;
}
.row-fluid .span5 {
	width: 40.05%;
}
.row-fluid .span4 {
	width: 31.49%;
}
.row-fluid .span3 {
	width: 22.92%;
}
.row-fluid .span2 {
	width: 14.36%;
}
.row-fluid .span1 {
	width: 5.8%;
}
input {
	margin-left: 0px;
}
textarea {
	margin-left: 0px;
}
.uneditable-input {
	margin-left: 0px;
}
input.span12 {
	width: 714px;
}
textarea.span12 {
	width: 714px;
}
.span12.uneditable-input {
	width: 714px;
}
input.span11 {
	width: 652px;
}
textarea.span11 {
	width: 652px;
}
.span11.uneditable-input {
	width: 652px;
}
input.span10 {
	width: 590px;
}
textarea.span10 {
	width: 590px;
}
.span10.uneditable-input {
	width: 590px;
}
input.span9 {
	width: 528px;
}
textarea.span9 {
	width: 528px;
}
.span9.uneditable-input {
	width: 528px;
}
input.span8 {
	width: 466px;
}
textarea.span8 {
	width: 466px;
}
.span8.uneditable-input {
	width: 466px;
}
input.span7 {
	width: 404px;
}
textarea.span7 {
	width: 404px;
}
.span7.uneditable-input {
	width: 404px;
}
input.span6 {
	width: 342px;
}
textarea.span6 {
	width: 342px;
}
.span6.uneditable-input {
	width: 342px;
}
input.span5 {
	width: 280px;
}
textarea.span5 {
	width: 280px;
}
.span5.uneditable-input {
	width: 280px;
}
input.span4 {
	width: 218px;
}
textarea.span4 {
	width: 218px;
}
.span4.uneditable-input {
	width: 218px;
}
input.span3 {
	width: 156px;
}
textarea.span3 {
	width: 156px;
}
.span3.uneditable-input {
	width: 156px;
}
input.span2 {
	width: 94px;
}
textarea.span2 {
	width: 94px;
}
.span2.uneditable-input {
	width: 94px;
}
input.span1 {
	width: 32px;
}
textarea.span1 {
	width: 32px;
}
.span1.uneditable-input {
	width: 32px;
}
}
@media all and (min-width:1200px)
{
.row {
	margin-left: -30px;
}
.row::before {
	display: table; content: "";
}
.row::after {
	display: table; content: "";
}
.row::after {
	clear: both;
}
[class*='span'] {
	margin-left: 30px; float: left;
}
.container {
	width: 1170px;
}
.navbar-fixed-top .container {
	width: 1170px;
}
.navbar-fixed-bottom .container {
	width: 1170px;
}
.span12 {
	width: 1170px;
}
.span11 {
	width: 1070px;
}
.span10 {
	width: 970px;
}
.span9 {
	width: 870px;
}
.span8 {
	width: 770px;
}
.span7 {
	width: 670px;
}
.span6 {
	width: 570px;
}
.span5 {
	width: 470px;
}
.span4 {
	width: 370px;
}
.span3 {
	width: 270px;
}
.span2 {
	width: 170px;
}
.span1 {
	width: 70px;
}
.offset12 {
	margin-left: 1230px;
}
.offset11 {
	margin-left: 1130px;
}
.offset10 {
	margin-left: 1030px;
}
.offset9 {
	margin-left: 930px;
}
.offset8 {
	margin-left: 830px;
}
.offset7 {
	margin-left: 730px;
}
.offset6 {
	margin-left: 630px;
}
.offset5 {
	margin-left: 530px;
}
.offset4 {
	margin-left: 430px;
}
.offset3 {
	margin-left: 330px;
}
.offset2 {
	margin-left: 230px;
}
.offset1 {
	margin-left: 130px;
}
.row-fluid {
	width: 100%;
}
.row-fluid::before {
	display: table; content: "";
}
.row-fluid::after {
	display: table; content: "";
}
.row-fluid::after {
	clear: both;
}
.row-fluid [class*='span'] {
	width: 100%; margin-left: 2.56%; float: left; display: block; min-height: 28px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
.row-fluid [class*='span']:first-child {
	margin-left: 0px;
}
.row-fluid .span12 {
	width: 100%;
}
.row-fluid .span11 {
	width: 91.45%;
}
.row-fluid .span10 {
	width: 82.9%;
}
.row-fluid .span9 {
	width: 74.35%;
}
.row-fluid .span8 {
	width: 65.81%;
}
.row-fluid .span7 {
	width: 57.26%;
}
.row-fluid .span6 {
	width: 48.71%;
}
.row-fluid .span5 {
	width: 40.17%;
}
.row-fluid .span4 {
	width: 31.62%;
}
.row-fluid .span3 {
	width: 23.07%;
}
.row-fluid .span2 {
	width: 14.52%;
}
.row-fluid .span1 {
	width: 5.98%;
}
input {
	margin-left: 0px;
}
textarea {
	margin-left: 0px;
}
.uneditable-input {
	margin-left: 0px;
}
input.span12 {
	width: 1160px;
}
textarea.span12 {
	width: 1160px;
}
.span12.uneditable-input {
	width: 1160px;
}
input.span11 {
	width: 1060px;
}
textarea.span11 {
	width: 1060px;
}
.span11.uneditable-input {
	width: 1060px;
}
input.span10 {
	width: 960px;
}
textarea.span10 {
	width: 960px;
}
.span10.uneditable-input {
	width: 960px;
}
input.span9 {
	width: 860px;
}
textarea.span9 {
	width: 860px;
}
.span9.uneditable-input {
	width: 860px;
}
input.span8 {
	width: 760px;
}
textarea.span8 {
	width: 760px;
}
.span8.uneditable-input {
	width: 760px;
}
input.span7 {
	width: 660px;
}
textarea.span7 {
	width: 660px;
}
.span7.uneditable-input {
	width: 660px;
}
input.span6 {
	width: 560px;
}
textarea.span6 {
	width: 560px;
}
.span6.uneditable-input {
	width: 560px;
}
input.span5 {
	width: 460px;
}
textarea.span5 {
	width: 460px;
}
.span5.uneditable-input {
	width: 460px;
}
input.span4 {
	width: 360px;
}
textarea.span4 {
	width: 360px;
}
.span4.uneditable-input {
	width: 360px;
}
input.span3 {
	width: 260px;
}
textarea.span3 {
	width: 260px;
}
.span3.uneditable-input {
	width: 260px;
}
input.span2 {
	width: 160px;
}
textarea.span2 {
	width: 160px;
}
.span2.uneditable-input {
	width: 160px;
}
input.span1 {
	width: 60px;
}
textarea.span1 {
	width: 60px;
}
.span1.uneditable-input {
	width: 60px;
}
.thumbnails {
	margin-left: -30px;
}
.thumbnails > li {
	margin-left: 30px;
}
.row-fluid .thumbnails {
	margin-left: 0px;
}
}
@media all and (max-width:979px)
{
body {
	padding-top: 0px;
}
.navbar-fixed-top {
	position: static;
}
.navbar-fixed-bottom {
	position: static;
}
.navbar-fixed-top {
	margin-bottom: 18px;
}
.navbar-fixed-bottom {
	margin-top: 18px;
}
.navbar-fixed-top .navbar-inner {
	padding: 5px;
}
.navbar-fixed-bottom .navbar-inner {
	padding: 5px;
}
.navbar .container {
	padding: 0px; width: auto;
}
.navbar .brand {
	margin: 0px 0px 0px -5px; padding-right: 10px; padding-left: 10px;
}
.nav-collapse {
	clear: both;
}
.nav-collapse .nav {
	margin: 0px 0px 9px; float: none;
}
.nav-collapse .nav > li {
	float: none;
}
.nav-collapse .nav > li > a {
	margin-bottom: 2px;
}
.nav-collapse .nav > .divider-vertical {
	display: none;
}
.nav-collapse .nav .nav-header {
	color: rgb(153, 153, 153); text-shadow: none;
}
.nav-collapse .nav > li > a {
	padding: 6px 15px; border-radius: 3px; color: rgb(153, 153, 153); font-weight: bold; -webkit-border-radius: 3px; -moz-border-radius: 3px;
}
.nav-collapse .dropdown-menu a {
	padding: 6px 15px; border-radius: 3px; color: rgb(153, 153, 153); font-weight: bold; -webkit-border-radius: 3px; -moz-border-radius: 3px;
}
.nav-collapse .btn {
	padding: 4px 10px; border-radius: 4px; font-weight: normal; -webkit-border-radius: 4px; -moz-border-radius: 4px;
}
.nav-collapse .dropdown-menu li + li a {
	margin-bottom: 2px;
}
.nav-collapse .nav > li > a:hover {
	background-color: rgb(34, 34, 34);
}
.nav-collapse .dropdown-menu a:hover {
	background-color: rgb(34, 34, 34);
}
.in.nav-collapse .btn-group {
	padding: 0px; margin-top: 5px;
}
.nav-collapse .dropdown-menu {
	margin: 0px 15px; padding: 0px; border-radius: 0px; border: currentColor; border-image: none; left: auto; top: auto; float: none; display: block; position: static; max-width: none; box-shadow: none; background-color: transparent; -webkit-border-radius: 0; -moz-border-radius: 0; -webkit-box-shadow: none; -moz-box-shadow: none;
}
.nav-collapse .dropdown-menu::before {
	display: none;
}
.nav-collapse .dropdown-menu::after {
	display: none;
}
.nav-collapse .dropdown-menu .divider {
	display: none;
}
.nav-collapse .navbar-form {
	margin: 9px 0px; padding: 9px 15px; border-top-color: rgb(34, 34, 34); border-bottom-color: rgb(34, 34, 34); border-top-width: 1px; border-bottom-width: 1px; border-top-style: solid; border-bottom-style: solid; float: none; box-shadow: inset 0px 1px 0px rgba(255,255,255,0.1), 0px 1px 0px rgba(255,255,255,0.1); -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1); -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
}
.nav-collapse .navbar-search {
	margin: 9px 0px; padding: 9px 15px; border-top-color: rgb(34, 34, 34); border-bottom-color: rgb(34, 34, 34); border-top-width: 1px; border-bottom-width: 1px; border-top-style: solid; border-bottom-style: solid; float: none; box-shadow: inset 0px 1px 0px rgba(255,255,255,0.1), 0px 1px 0px rgba(255,255,255,0.1); -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1); -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
}
.navbar .nav-collapse .pull-right.nav {
	margin-left: 0px; float: none;
}
.nav-collapse {
	height: 0px; overflow: hidden;
}
.collapse.nav-collapse {
	height: 0px; overflow: hidden;
}
.navbar .btn-navbar {
	display: block;
}
.navbar-static .navbar-inner {
	padding-right: 10px; padding-left: 10px;
}
}
@media all and (min-width:980px)
{
.collapse.nav-collapse {
	height: auto !important; overflow: visible !important;
}
}
