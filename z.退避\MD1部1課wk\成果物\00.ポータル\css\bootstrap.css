article {
	display: block;
}
aside {
	display: block;
}
details {
	display: block;
}
figcaption {
	display: block;
}
figure {
	display: block;
}
footer {
	display: block;
}
header {
	display: block;
}
hgroup {
	display: block;
}
nav {
	display: block;
}
section {
	display: block;
}
audio {
	display: inline-block;
}
canvas {
	display: inline-block;
}
video {
	display: inline-block;
}
audio:not([controls]) {
	display: none;
}
html {
	font-size: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;
}
a:focus {
	outline: rgb(51, 51, 51) dotted thin; outline-offset: -2px;
}
a:hover {
	outline: 0px;
}
a:active {
	outline: 0px;
}
sub {
	line-height: 0; font-size: 75%; vertical-align: baseline; position: relative;
}
sup {
	line-height: 0; font-size: 75%; vertical-align: baseline; position: relative;
}
sup {
	top: -0.5em;
}
sub {
	bottom: -0.25em;
}
img {
	border: 0px currentColor; border-image: none; vertical-align: middle; -ms-interpolation-mode: bicubic; max-width: 100%;
}
#map_canvas img {
	max-width: none;
}
button {
	margin: 0px; font-size: 100%; vertical-align: middle;
}
input {
	margin: 0px; font-size: 100%; vertical-align: middle;
}
select {
	margin: 0px; font-size: 100%; vertical-align: middle;
}
textarea {
	margin: 0px; font-size: 100%; vertical-align: middle;
}
button {
	line-height: normal;
}
input {
	line-height: normal;
}
button {
	cursor: pointer; -webkit-appearance: button;
}
input[type='button'] {
	cursor: pointer; -webkit-appearance: button;
}
input[type='reset'] {
	cursor: pointer; -webkit-appearance: button;
}
input[type='submit'] {
	cursor: pointer; -webkit-appearance: button;
}
input[type='search'] {
	box-sizing: content-box; -webkit-appearance: textfield; -webkit-box-sizing: content-box; -moz-box-sizing: content-box;
}
textarea {
	overflow: auto; vertical-align: top;
}
.clearfix {
	
}
.clearfix::before {
	display: table; content: "";
}
.clearfix::after {
	display: table; content: "";
}
.clearfix::after {
	clear: both;
}
.hide-text {
	font: 0px/0 a; border: 0px currentColor; border-image: none; color: transparent; font-size-adjust: none; font-stretch: normal; text-shadow: none; background-color: transparent;
}
.input-block-level {
	width: 100%; display: block; min-height: 28px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
body {
	margin: 0px; color: rgb(119, 119, 119); line-height: 18px; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 13px;
}
a {
	color: rgb(0, 136, 204); text-decoration: none;
}
a:hover {
	color: rgb(0, 85, 128); text-decoration: underline;
}
.row {
	margin-left: -20px;
}
.row::before {
	display: table; content: "";
}
.row::after {
	display: table; content: "";
}
.row::after {
	clear: both;
}
[class*='span'] {
	margin-left: 20px; float: left;
}
.container {
	width: 940px;
}
.navbar-fixed-top .container {
	width: 940px;
}
.navbar-fixed-bottom .container {
	width: 940px;
}
.span12 {
	width: 940px;
}
.span11 {
	width: 860px;
}
.span10 {
	width: 780px;
}
.span9 {
	width: 700px;
}
.span8 {
	width: 620px;
}
.span7 {
	width: 540px;
}
.span6 {
	width: 460px;
}
.span5 {
	width: 380px;
}
.span4 {
	width: 300px;
}
.span3 {
	width: 220px;
}
.span2 {
	width: 140px;
}
.span1 {
	width: 60px;
}
.offset12 {
	margin-left: 980px;
}
.offset11 {
	margin-left: 900px;
}
.offset10 {
	margin-left: 820px;
}
.offset9 {
	margin-left: 740px;
}
.offset8 {
	margin-left: 660px;
}
.offset7 {
	margin-left: 580px;
}
.offset6 {
	margin-left: 500px;
}
.offset5 {
	margin-left: 420px;
}
.offset4 {
	margin-left: 340px;
}
.offset3 {
	margin-left: 260px;
}
.offset2 {
	margin-left: 180px;
}
.offset1 {
	margin-left: 100px;
}
.row-fluid {
	width: 100%;
}
.row-fluid::before {
	display: table; content: "";
}
.row-fluid::after {
	display: table; content: "";
}
.row-fluid::after {
	clear: both;
}
.row-fluid [class*='span'] {
	width: 100%; margin-left: 2.12%; float: left; display: block; min-height: 28px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box;
}
.row-fluid [class*='span']:first-child {
	margin-left: 0px;
}
.row-fluid .span12 {
	width: 99.99%;
}
.row-fluid .span11 {
	width: 91.48%;
}
.row-fluid .span10 {
	width: 82.97%;
}
.row-fluid .span9 {
	width: 74.46%;
}
.row-fluid .span8 {
	width: 65.95%;
}
.row-fluid .span7 {
	width: 57.44%;
}
.row-fluid .span6 {
	width: 48.93%;
}
.row-fluid .span5 {
	width: 40.42%;
}
.row-fluid .span4 {
	width: 31.91%;
}
.row-fluid .span3 {
	width: 23.4%;
}
.row-fluid .span2 {
	width: 14.89%;
}
.row-fluid .span1 {
	width: 6.38%;
}
.row-fluid .offset12 {
	margin-left: 102.12% !important;
}
.row-fluid > [class*='span'].offset12 {
	margin-left: 102.12% !important;
}
.row-fluid .offset11 {
	margin-left: 93.61% !important;
}
.row-fluid > [class*='span'].offset11 {
	margin-left: 93.61% !important;
}
.row-fluid .offset10 {
	margin-left: 85.1% !important;
}
.row-fluid > [class*='span'].offset10 {
	margin-left: 85.1% !important;
}
.row-fluid .offset9 {
	margin-left: 76.59% !important;
}
.row-fluid > [class*='span'].offset9 {
	margin-left: 76.59% !important;
}
.row-fluid .offset8 {
	margin-left: 68.08% !important;
}
.row-fluid > [class*='span'].offset8 {
	margin-left: 68.08% !important;
}
.row-fluid .offset7 {
	margin-left: 59.57% !important;
}
.row-fluid > [class*='span'].offset7 {
	margin-left: 59.57% !important;
}
.row-fluid .offset6 {
	margin-left: 51.06% !important;
}
.row-fluid > [class*='span'].offset6 {
	margin-left: 51.06% !important;
}
.row-fluid .offset5 {
	margin-left: 42.55% !important;
}
.row-fluid > [class*='span'].offset5 {
	margin-left: 42.55% !important;
}
.row-fluid .offset4 {
	margin-left: 34.04% !important;
}
.row-fluid > [class*='span'].offset4 {
	margin-left: 34.04% !important;
}
.row-fluid .offset3 {
	margin-left: 25.53% !important;
}
.row-fluid > [class*='span'].offset3 {
	margin-left: 25.53% !important;
}
.row-fluid .offset2 {
	margin-left: 17.02% !important;
}
.row-fluid > [class*='span'].offset2 {
	margin-left: 17.02% !important;
}
.row-fluid .offset1 {
	margin-left: 8.51% !important;
}
.row-fluid > [class*='span'].offset1 {
	margin-left: 8.51% !important;
}
.container {
	margin-right: auto; margin-left: auto;
}
.container::before {
	display: table; content: "";
}
.container::after {
	display: table; content: "";
}
.container::after {
	clear: both;
}
.container-fluid {
	padding-right: 20px; padding-left: 20px;
}
.container-fluid::before {
	display: table; content: "";
}
.container-fluid::after {
	display: table; content: "";
}
.container-fluid::after {
	clear: both;
}
p {
	margin: 0px 0px 9px;
}
p small {
	color: rgb(153, 153, 153); font-size: 10px;
}
.lead {
	line-height: 27px; font-size: 20px; font-weight: 200; margin-bottom: 18px;
}
h1 {
	margin: 0px; color: inherit; font-family: inherit; font-weight: bold; text-rendering: optimizelegibility;
}
h2 {
	margin: 0px; color: inherit; font-family: inherit; font-weight: bold; text-rendering: optimizelegibility;
}
h3 {
	margin: 0px; color: inherit; font-family: inherit; font-weight: bold; text-rendering: optimizelegibility;
}
h4 {
	margin: 0px; color: inherit; font-family: inherit; font-weight: bold; text-rendering: optimizelegibility;
}
h5 {
	margin: 0px; color: inherit; font-family: inherit; font-weight: bold; text-rendering: optimizelegibility;
}
h6 {
	margin: 0px; color: inherit; font-family: inherit; font-weight: bold; text-rendering: optimizelegibility;
}
h1 small {
	color: rgb(153, 153, 153); font-weight: normal;
}
h2 small {
	color: rgb(153, 153, 153); font-weight: normal;
}
h3 small {
	color: rgb(153, 153, 153); font-weight: normal;
}
h4 small {
	color: rgb(153, 153, 153); font-weight: normal;
}
h5 small {
	color: rgb(153, 153, 153); font-weight: normal;
}
h6 small {
	color: rgb(153, 153, 153); font-weight: normal;
}
h1 {
	line-height: 36px; font-size: 30px;
}
h1 small {
	font-size: 18px;
}
h2 {
	line-height: 36px; font-size: 24px;
}
h2 small {
	font-size: 18px;
}
h3 {
	line-height: 27px; font-size: 18px;
}
h3 small {
	font-size: 14px;
}
h4 {
	line-height: 18px;
}
h5 {
	line-height: 18px;
}
h6 {
	line-height: 18px;
}
h4 {
	font-size: 14px;
}
h4 small {
	font-size: 12px;
}
h5 {
	font-size: 12px;
}
h6 {
	color: rgb(153, 153, 153); text-transform: uppercase; font-size: 11px;
}
.page-header {
	margin: 18px 0px; padding-bottom: 17px; border-bottom-color: rgb(238, 238, 238); border-bottom-width: 1px; border-bottom-style: solid;
}
.page-header h1 {
	line-height: 1;
}
ul {
	margin: 0px 0px 9px 25px; padding: 0px;
}
ol {
	margin: 0px 0px 9px 25px; padding: 0px;
}
ul ul {
	margin-bottom: 0px;
}
ul ol {
	margin-bottom: 0px;
}
ol ol {
	margin-bottom: 0px;
}
ol ul {
	margin-bottom: 0px;
}
ul {
	
}
ol {
	list-style: decimal;
}
li {
	line-height: 18px;
}
ul.unstyled {
	list-style: none; margin-left: 0px;
}
ol.unstyled {
	list-style: none; margin-left: 0px;
}
dl {
	margin-bottom: 18px;
}
dt {
	line-height: 18px;
}
dd {
	line-height: 18px;
}
dt {
	line-height: 17px; font-weight: bold;
}
dd {
	margin-left: 9px;
}
.dl-horizontal dt {
	width: 120px; text-align: right; overflow: hidden; clear: left; float: left; white-space: nowrap; -ms-text-overflow: ellipsis;
}
.dl-horizontal dd {
	margin-left: 130px;
}
hr {
	border-width: 1px 0px; border-style: solid none; border-color: rgb(238, 238, 238) currentColor rgb(255, 255, 255); margin: 18px 0px; border-image: none;
}
strong {
	font-weight: bold;
}
em {
	font-style: italic;
}
.muted {
	color: rgb(153, 153, 153);
}
abbr[title] {
	border-bottom-color: rgb(153, 153, 153); border-bottom-width: 1px; border-bottom-style: dotted; cursor: help;
}
abbr.initialism {
	text-transform: uppercase; font-size: 90%;
}
blockquote {
	margin: 0px 0px 18px; padding: 0px 0px 0px 15px; border-left-color: rgb(238, 238, 238); border-left-width: 5px; border-left-style: solid;
}
blockquote p {
	line-height: 22.5px; font-size: 16px; font-weight: 300; margin-bottom: 0px;
}
blockquote small {
	color: rgb(153, 153, 153); line-height: 18px; display: block;
}
blockquote small::before {
	content: "\2014 \00A0";
}
blockquote.pull-right {
	padding-right: 15px; padding-left: 0px; border-right-color: rgb(238, 238, 238); border-left-color: currentColor; border-right-width: 5px; border-left-width: 0px; border-right-style: solid; border-left-style: none; float: right;
}
blockquote.pull-right p {
	text-align: right;
}
blockquote.pull-right small {
	text-align: right;
}
q::before {
	content: "";
}
q::after {
	content: "";
}
blockquote::before {
	content: "";
}
blockquote::after {
	content: "";
}
address {
	line-height: 18px; font-style: normal; margin-bottom: 18px; display: block;
}
small {
	font-size: 100%;
}
cite {
	font-style: normal;
}
code {
	padding: 0px 3px 2px; border-radius: 3px; color: rgb(51, 51, 51); font-family: Menlo, Monaco, Consolas, "Courier New", monospace; font-size: 12px; -webkit-border-radius: 3px; -moz-border-radius: 3px;
}
pre {
	padding: 0px 3px 2px; border-radius: 3px; color: rgb(51, 51, 51); font-family: Menlo, Monaco, Consolas, "Courier New", monospace; font-size: 12px; -webkit-border-radius: 3px; -moz-border-radius: 3px;
}
code {
	padding: 2px 4px; border: 1px solid rgb(225, 225, 232); border-image: none; color: rgb(221, 17, 68); background-color: rgb(247, 247, 249);
}
pre {
	margin: 0px 0px 9px; padding: 8.5px; border-radius: 4px; border: 1px solid rgba(0, 0, 0, 0.15); border-image: none; line-height: 18px; font-size: 12.02px; display: block; white-space: pre-wrap; -ms-word-break: break-all; -ms-word-wrap: break-word; background-color: rgb(245, 245, 245); -webkit-border-radius: 4px; -moz-border-radius: 4px;
}
pre.prettyprint {
	margin-bottom: 18px;
}
pre code {
	padding: 0px; border: 0px currentColor; border-image: none; color: inherit; background-color: transparent;
}
.pre-scrollable {
	-ms-overflow-y: scroll; max-height: 340px;
}
form {
	margin: 0px 0px 18px;
}
fieldset {
	margin: 0px; padding: 0px; border: 0px currentColor; border-image: none;
}
legend {
	border-width: 0px 0px 1px; border-style: none none solid; border-color: currentColor currentColor rgb(229, 229, 229); padding: 0px; border-image: none; width: 100%; color: rgb(51, 51, 51); line-height: 36px; font-size: 19.5px; margin-bottom: 27px; display: block;
}
legend small {
	color: rgb(153, 153, 153); font-size: 13.5px;
}
label {
	line-height: 18px; font-size: 13px; font-weight: normal;
}
input {
	line-height: 18px; font-size: 13px; font-weight: normal;
}
button {
	line-height: 18px; font-size: 13px; font-weight: normal;
}
select {
	line-height: 18px; font-size: 13px; font-weight: normal;
}
textarea {
	line-height: 18px; font-size: 13px; font-weight: normal;
}
input {
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}
button {
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}
select {
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}
textarea {
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}
label {
	margin-bottom: 5px; display: block;
}
select {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
textarea {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='text'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='password'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='datetime'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='datetime-local'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='date'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='month'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='time'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='week'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='number'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='email'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='url'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='search'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='tel'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input[type='color'] {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
.uneditable-input {
	padding: 4px; height: 18px; color: rgb(85, 85, 85); line-height: 18px; font-size: 13px; margin-bottom: 9px; display: inline-block;
}
input {
	width: 210px;
}
textarea {
	width: 210px;
}
textarea {
	height: auto;
}
textarea {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='text'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='password'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='datetime'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='datetime-local'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='date'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='month'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='time'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='week'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='number'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='email'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='url'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='search'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='tel'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
input[type='color'] {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
.uneditable-input {
	border-radius: 3px; border: 1px solid rgb(204, 204, 204); transition:border 0.2s linear, box-shadow 0.2s linear; border-image: none; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075); background-color: rgb(255, 255, 255); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); -webkit-transition: border linear 0.2s, box-shadow linear 0.2s; -moz-transition: border linear 0.2s, box-shadow linear 0.2s; -o-transition: border linear 0.2s, box-shadow linear 0.2s;
}
textarea:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='text']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='password']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='datetime']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='datetime-local']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='date']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='month']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='time']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='week']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='number']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='email']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='url']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='search']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='tel']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='color']:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
.uneditable-input:focus {
	border-color: rgba(82, 168, 236, 0.8); outline: 0px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.075), 0px 0px 8px rgba(82,168,236,0.6); -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
}
input[type='radio'] {
	margin: 3px 0px; line-height: normal; cursor: pointer;
}
input[type='checkbox'] {
	margin: 3px 0px; line-height: normal; cursor: pointer;
}
input[type='submit'] {
	width: auto;
}
input[type='reset'] {
	width: auto;
}
input[type='button'] {
	width: auto;
}
input[type='radio'] {
	width: auto;
}
input[type='checkbox'] {
	width: auto;
}
.uneditable-textarea {
	width: auto; height: auto;
}
select {
	height: 28px; line-height: 28px;
}
input[type='file'] {
	height: 28px; line-height: 28px;
}
select {
	border: 1px solid rgb(187, 187, 187); border-image: none; width: 220px;
}
select[multiple] {
	height: auto;
}
select[size] {
	height: auto;
}
select:focus {
	outline: rgb(51, 51, 51) dotted thin; outline-offset: -2px;
}
input[type='file']:focus {
	outline: rgb(51, 51, 51) dotted thin; outline-offset: -2px;
}
input[type='radio']:focus {
	outline: rgb(51, 51, 51) dotted thin; outline-offset: -2px;
}
input[type='checkbox']:focus {
	outline: rgb(51, 51, 51) dotted thin; outline-offset: -2px;
}
.radio {
	padding-left: 18px; min-height: 18px;
}
.checkbox {
	padding-left: 18px; min-height: 18px;
}
.radio input[type='radio'] {
	margin-left: -18px; float: left;
}
.checkbox input[type='checkbox'] {
	margin-left: -18px; float: left;
}
.controls > :first-child.radio {
	padding-top: 5px;
}
.controls > :first-child.checkbox {
	padding-top: 5px;
}
.inline.radio {
	padding-top: 5px; margin-bottom: 0px; vertical-align: middle; display: inline-block;
}
.inline.checkbox {
	padding-top: 5px; margin-bottom: 0px; vertical-align: middle; display: inline-block;
}
.inline.radio + .inline.radio {
	margin-left: 10px;
}
.inline.checkbox + .inline.checkbox {
	margin-left: 10px;
}
.input-mini {
	width: 60px;
}
.input-small {
	width: 90px;
}
.input-medium {
	width: 150px;
}
.input-large {
	width: 210px;
}
.input-xlarge {
	width: 270px;
}
.input-xxlarge {
	width: 530px;
}
input[class*='span'] {
	margin-left: 0px; float: none;
}
select[class*='span'] {
	margin-left: 0px; float: none;
}
textarea[class*='span'] {
	margin-left: 0px; float: none;
}
[class*='span'].uneditable-input {
	margin-left: 0px; float: none;
}
.row-fluid input[class*='span'] {
	margin-left: 0px; float: none;
}
.row-fluid select[class*='span'] {
	margin-left: 0px; float: none;
}
.row-fluid textarea[class*='span'] {
	margin-left: 0px; float: none;
}
.row-fluid [class*='span'].uneditable-input {
	margin-left: 0px; float: none;
}
.input-append input[class*='span'] {
	display: inline-block;
}
.input-append [class*='span'].uneditable-input {
	display: inline-block;
}
.input-prepend input[class*='span'] {
	display: inline-block;
}
.input-prepend [class*='span'].uneditable-input {
	display: inline-block;
}
.row-fluid .input-prepend [class*='span'] {
	display: inline-block;
}
.row-fluid .input-append [class*='span'] {
	display: inline-block;
}
input {
	margin-left: 0px;
}
textarea {
	margin-left: 0px;
}
.uneditable-input {
	margin-left: 0px;
}
input.span12 {
	width: 930px;
}
textarea.span12 {
	width: 930px;
}
.span12.uneditable-input {
	width: 930px;
}
input.span11 {
	width: 850px;
}
textarea.span11 {
	width: 850px;
}
.span11.uneditable-input {
	width: 850px;
}
input.span10 {
	width: 770px;
}
textarea.span10 {
	width: 770px;
}
.span10.uneditable-input {
	width: 770px;
}
input.span9 {
	width: 690px;
}
textarea.span9 {
	width: 690px;
}
.span9.uneditable-input {
	width: 690px;
}
input.span8 {
	width: 610px;
}
textarea.span8 {
	width: 610px;
}
.span8.uneditable-input {
	width: 610px;
}
input.span7 {
	width: 530px;
}
textarea.span7 {
	width: 530px;
}
.span7.uneditable-input {
	width: 530px;
}
input.span6 {
	width: 450px;
}
textarea.span6 {
	width: 450px;
}
.span6.uneditable-input {
	width: 450px;
}
input.span5 {
	width: 370px;
}
textarea.span5 {
	width: 370px;
}
.span5.uneditable-input {
	width: 370px;
}
input.span4 {
	width: 290px;
}
textarea.span4 {
	width: 290px;
}
.span4.uneditable-input {
	width: 290px;
}
input.span3 {
	width: 210px;
}
textarea.span3 {
	width: 210px;
}
.span3.uneditable-input {
	width: 210px;
}
input.span2 {
	width: 130px;
}
textarea.span2 {
	width: 130px;
}
.span2.uneditable-input {
	width: 130px;
}
input.span1 {
	width: 50px;
}
textarea.span1 {
	width: 50px;
}
.span1.uneditable-input {
	width: 50px;
}
input[disabled] {
	border-color: rgb(221, 221, 221); cursor: not-allowed; background-color: rgb(238, 238, 238);
}
select[disabled] {
	border-color: rgb(221, 221, 221); cursor: not-allowed; background-color: rgb(238, 238, 238);
}
textarea[disabled] {
	border-color: rgb(221, 221, 221); cursor: not-allowed; background-color: rgb(238, 238, 238);
}
input[readonly] {
	border-color: rgb(221, 221, 221); cursor: not-allowed; background-color: rgb(238, 238, 238);
}
select[readonly] {
	border-color: rgb(221, 221, 221); cursor: not-allowed; background-color: rgb(238, 238, 238);
}
textarea[readonly] {
	border-color: rgb(221, 221, 221); cursor: not-allowed; background-color: rgb(238, 238, 238);
}
input[type='radio'][disabled] {
	background-color: transparent;
}
input[type='checkbox'][disabled] {
	background-color: transparent;
}
input[type='radio'][readonly] {
	background-color: transparent;
}
input[type='checkbox'][readonly] {
	background-color: transparent;
}
.warning.control-group > label {
	color: rgb(192, 152, 83);
}
.warning.control-group .help-block {
	color: rgb(192, 152, 83);
}
.warning.control-group .help-inline {
	color: rgb(192, 152, 83);
}
.warning.control-group .checkbox {
	border-color: rgb(192, 152, 83); color: rgb(192, 152, 83);
}
.warning.control-group .radio {
	border-color: rgb(192, 152, 83); color: rgb(192, 152, 83);
}
.warning.control-group input {
	border-color: rgb(192, 152, 83); color: rgb(192, 152, 83);
}
.warning.control-group select {
	border-color: rgb(192, 152, 83); color: rgb(192, 152, 83);
}
.warning.control-group textarea {
	border-color: rgb(192, 152, 83); color: rgb(192, 152, 83);
}
.warning.control-group .checkbox:focus {
	border-color: rgb(164, 126, 60); box-shadow: 0px 0px 6px #dbc59e; -webkit-box-shadow: 0 0 6px #dbc59e; -moz-box-shadow: 0 0 6px #dbc59e;
}
.warning.control-group .radio:focus {
	border-color: rgb(164, 126, 60); box-shadow: 0px 0px 6px #dbc59e; -webkit-box-shadow: 0 0 6px #dbc59e; -moz-box-shadow: 0 0 6px #dbc59e;
}
.warning.control-group input:focus {
	border-color: rgb(164, 126, 60); box-shadow: 0px 0px 6px #dbc59e; -webkit-box-shadow: 0 0 6px #dbc59e; -moz-box-shadow: 0 0 6px #dbc59e;
}
.warning.control-group select:focus {
	border-color: rgb(164, 126, 60); box-shadow: 0px 0px 6px #dbc59e; -webkit-box-shadow: 0 0 6px #dbc59e; -moz-box-shadow: 0 0 6px #dbc59e;
}
.warning.control-group textarea:focus {
	border-color: rgb(164, 126, 60); box-shadow: 0px 0px 6px #dbc59e; -webkit-box-shadow: 0 0 6px #dbc59e; -moz-box-shadow: 0 0 6px #dbc59e;
}
.warning.control-group .input-prepend .add-on {
	border-color: rgb(192, 152, 83); color: rgb(192, 152, 83); background-color: rgb(252, 248, 227);
}
.warning.control-group .input-append .add-on {
	border-color: rgb(192, 152, 83); color: rgb(192, 152, 83); background-color: rgb(252, 248, 227);
}
.error.control-group > label {
	color: rgb(185, 74, 72);
}
.error.control-group .help-block {
	color: rgb(185, 74, 72);
}
.error.control-group .help-inline {
	color: rgb(185, 74, 72);
}
.error.control-group .checkbox {
	border-color: rgb(185, 74, 72); color: rgb(185, 74, 72);
}
.error.control-group .radio {
	border-color: rgb(185, 74, 72); color: rgb(185, 74, 72);
}
.error.control-group input {
	border-color: rgb(185, 74, 72); color: rgb(185, 74, 72);
}
.error.control-group select {
	border-color: rgb(185, 74, 72); color: rgb(185, 74, 72);
}
.error.control-group textarea {
	border-color: rgb(185, 74, 72); color: rgb(185, 74, 72);
}
.error.control-group .checkbox:focus {
	border-color: rgb(149, 59, 57); box-shadow: 0px 0px 6px #d59392; -webkit-box-shadow: 0 0 6px #d59392; -moz-box-shadow: 0 0 6px #d59392;
}
.error.control-group .radio:focus {
	border-color: rgb(149, 59, 57); box-shadow: 0px 0px 6px #d59392; -webkit-box-shadow: 0 0 6px #d59392; -moz-box-shadow: 0 0 6px #d59392;
}
.error.control-group input:focus {
	border-color: rgb(149, 59, 57); box-shadow: 0px 0px 6px #d59392; -webkit-box-shadow: 0 0 6px #d59392; -moz-box-shadow: 0 0 6px #d59392;
}
.error.control-group select:focus {
	border-color: rgb(149, 59, 57); box-shadow: 0px 0px 6px #d59392; -webkit-box-shadow: 0 0 6px #d59392; -moz-box-shadow: 0 0 6px #d59392;
}
.error.control-group textarea:focus {
	border-color: rgb(149, 59, 57); box-shadow: 0px 0px 6px #d59392; -webkit-box-shadow: 0 0 6px #d59392; -moz-box-shadow: 0 0 6px #d59392;
}
.error.control-group .input-prepend .add-on {
	border-color: rgb(185, 74, 72); color: rgb(185, 74, 72); background-color: rgb(242, 222, 222);
}
.error.control-group .input-append .add-on {
	border-color: rgb(185, 74, 72); color: rgb(185, 74, 72); background-color: rgb(242, 222, 222);
}
.success.control-group > label {
	color: rgb(70, 136, 71);
}
.success.control-group .help-block {
	color: rgb(70, 136, 71);
}
.success.control-group .help-inline {
	color: rgb(70, 136, 71);
}
.success.control-group .checkbox {
	border-color: rgb(70, 136, 71); color: rgb(70, 136, 71);
}
.success.control-group .radio {
	border-color: rgb(70, 136, 71); color: rgb(70, 136, 71);
}
.success.control-group input {
	border-color: rgb(70, 136, 71); color: rgb(70, 136, 71);
}
.success.control-group select {
	border-color: rgb(70, 136, 71); color: rgb(70, 136, 71);
}
.success.control-group textarea {
	border-color: rgb(70, 136, 71); color: rgb(70, 136, 71);
}
.success.control-group .checkbox:focus {
	border-color: rgb(53, 102, 53); box-shadow: 0px 0px 6px #7aba7b; -webkit-box-shadow: 0 0 6px #7aba7b; -moz-box-shadow: 0 0 6px #7aba7b;
}
.success.control-group .radio:focus {
	border-color: rgb(53, 102, 53); box-shadow: 0px 0px 6px #7aba7b; -webkit-box-shadow: 0 0 6px #7aba7b; -moz-box-shadow: 0 0 6px #7aba7b;
}
.success.control-group input:focus {
	border-color: rgb(53, 102, 53); box-shadow: 0px 0px 6px #7aba7b; -webkit-box-shadow: 0 0 6px #7aba7b; -moz-box-shadow: 0 0 6px #7aba7b;
}
.success.control-group select:focus {
	border-color: rgb(53, 102, 53); box-shadow: 0px 0px 6px #7aba7b; -webkit-box-shadow: 0 0 6px #7aba7b; -moz-box-shadow: 0 0 6px #7aba7b;
}
.success.control-group textarea:focus {
	border-color: rgb(53, 102, 53); box-shadow: 0px 0px 6px #7aba7b; -webkit-box-shadow: 0 0 6px #7aba7b; -moz-box-shadow: 0 0 6px #7aba7b;
}
.success.control-group .input-prepend .add-on {
	border-color: rgb(70, 136, 71); color: rgb(70, 136, 71); background-color: rgb(223, 240, 216);
}
.success.control-group .input-append .add-on {
	border-color: rgb(70, 136, 71); color: rgb(70, 136, 71); background-color: rgb(223, 240, 216);
}
input:required:invalid:focus {
	border-color: rgb(238, 95, 91); color: rgb(185, 74, 72);
}
textarea:required:invalid:focus {
	border-color: rgb(238, 95, 91); color: rgb(185, 74, 72);
}
select:required:invalid:focus {
	border-color: rgb(238, 95, 91); color: rgb(185, 74, 72);
}
input:required:invalid:focus {
	border-color: rgb(233, 50, 45); box-shadow: 0px 0px 6px #f8b9b7; -webkit-box-shadow: 0 0 6px #f8b9b7; -moz-box-shadow: 0 0 6px #f8b9b7;
}
textarea:required:invalid:focus {
	border-color: rgb(233, 50, 45); box-shadow: 0px 0px 6px #f8b9b7; -webkit-box-shadow: 0 0 6px #f8b9b7; -moz-box-shadow: 0 0 6px #f8b9b7;
}
select:required:invalid:focus {
	border-color: rgb(233, 50, 45); box-shadow: 0px 0px 6px #f8b9b7; -webkit-box-shadow: 0 0 6px #f8b9b7; -moz-box-shadow: 0 0 6px #f8b9b7;
}
.form-actions {
	padding: 17px 20px 18px; margin-top: 18px; margin-bottom: 18px; border-top-color: rgb(229, 229, 229); border-top-width: 1px; border-top-style: solid; background-color: rgb(245, 245, 245);
}
.form-actions::before {
	display: table; content: "";
}
.form-actions::after {
	display: table; content: "";
}
.form-actions::after {
	clear: both;
}
.uneditable-input {
	border-color: rgb(238, 238, 238); overflow: hidden; white-space: nowrap; cursor: not-allowed; box-shadow: inset 0px 1px 2px rgba(0,0,0,0.025); background-color: rgb(255, 255, 255); -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.025); -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.025);
}
:-ms-input-placeholder {
	color: rgb(153, 153, 153);
}
.help-block {
	color: rgb(85, 85, 85);
}
.help-inline {
	color: rgb(85, 85, 85);
}
.help-block {
	margin-bottom: 9px; display: block;
}
.help-inline {
	padding-left: 5px; vertical-align: middle; display: inline-block;
}
.input-prepend {
	margin-bottom: 5px;
}
.input-append {
	margin-bottom: 5px;
}
.input-prepend input {
	border-radius: 0px 3px 3px 0px; margin-bottom: 0px; vertical-align: middle; position: relative; -webkit-border-radius: 0 3px 3px 0; -moz-border-radius: 0 3px 3px 0;
}
.input-append input {
	border-radius: 0px 3px 3px 0px; margin-bottom: 0px; vertical-align: middle; position: relative; -webkit-border-radius: 0 3px 3px 0; -moz-border-radius: 0 3px 3px 0;
}
.input-prepend select {
	border-radius: 0px 3px 3px 0px; margin-bottom: 0px; vertical-align: middle; position: relative; -webkit-border-radius: 0 3px 3px 0; -moz-border-radius: 0 3px 3px 0;
}
.input-append select {
	border-radius: 0px 3px 3px 0px; margin-bottom: 0px; vertical-align: middle; position: relative; -webkit-border-radius: 0 3px 3px 0; -moz-border-radius: 0 3px 3px 0;
}
.input-prepend .uneditable-input {
	border-radius: 0px 3px 3px 0px; margin-bottom: 0px; vertical-align: middle; position: relative; -webkit-border-radius: 0 3px 3px 0; -moz-border-radius: 0 3px 3px 0;
}
.input-append .uneditable-input {
	border-radius: 0px 3px 3px 0px; margin-bottom: 0px; vertical-align: middle; position: relative; -webkit-border-radius: 0 3px 3px 0; -moz-border-radius: 0 3px 3px 0;
}
.input-prepend input:focus {
	z-index: 2;
}
.input-append input:focus {
	z-index: 2;
}
.input-prepend select:focus {
	z-index: 2;
}
.input-append select:focus {
	z-index: 2;
}
.input-prepend .uneditable-input:focus {
	z-index: 2;
}
.input-append .uneditable-input:focus {
	z-index: 2;
}
.input-prepend .uneditable-input {
	border-left-color: rgb(204, 204, 204);
}
.input-append .uneditable-input {
	border-left-color: rgb(204, 204, 204);
}
.input-prepend .add-on {
	padding: 4px 5px; border: 1px solid rgb(204, 204, 204); border-image: none; width: auto; height: 18px; text-align: center; line-height: 18px; font-weight: normal; vertical-align: middle; display: inline-block; min-width: 16px; text-shadow: 0px 1px 0px #ffffff; background-color: rgb(238, 238, 238);
}
.input-append .add-on {
	padding: 4px 5px; border: 1px solid rgb(204, 204, 204); border-image: none; width: auto; height: 18px; text-align: center; line-height: 18px; font-weight: normal; vertical-align: middle; display: inline-block; min-width: 16px; text-shadow: 0px 1px 0px #ffffff; background-color: rgb(238, 238, 238);
}
.input-prepend .add-on {
	border-radius: 0px; margin-left: -1px; -webkit-border-radius: 0; -moz-border-radius: 0;
}
.input-append .add-on {
	border-radius: 0px; margin-left: -1px; -webkit-border-radius: 0; -moz-border-radius: 0;
}
.input-prepend .btn {
	border-radius: 0px; margin-left: -1px; -webkit-border-radius: 0; -moz-border-radius: 0;
}
.input-append .btn {
	border-radius: 0px; margin-left: -1px; -webkit-border-radius: 0; -moz-border-radius: 0;
}
.input-prepend .active {
	border-color: rgb(70, 165, 70); background-color: rgb(169, 219, 169);
}
.input-append .active {
	border-color: rgb(70, 165, 70); background-color: rgb(169, 219, 169);
}
.input-prepend .add-on {
	margin-right: -1px;
}
.input-prepend .btn {
	margin-right: -1px;
}
.input-prepend :first-child.add-on {
	border-radius: 3px 0px 0px 3px; -webkit-border-radius: 3px 0 0 3px; -moz-border-radius: 3px 0 0 3px;
}
.input-prepend :first-child.btn {
	border-radius: 3px 0px 0px 3px; -webkit-border-radius: 3px 0 0 3px; -moz-border-radius: 3px 0 0 3px;
}
.input-append input {
	border-radius: 3px 0px 0px 3px; -webkit-border-radius: 3px 0 0 3px; -moz-border-radius: 3px 0 0 3px;
}
.input-append select {
	border-radius: 3px 0px 0px 3px; -webkit-border-radius: 3px 0 0 3px; -moz-border-radius: 3px 0 0 3px;
}
.input-append .uneditable-input {
	border-radius: 3px 0px 0px 3px; -webkit-border-radius: 3px 0 0 3px; -moz-border-radius: 3px 0 0 3px;
}
.input-append .uneditable-input {
	border-right-color: rgb(204, 204, 204); border-left-color: rgb(238, 238, 238);
}
.input-append :last-child.add-on {
	border-radius: 0px 3px 3px 0px; -webkit-border-radius: 0 3px 3px 0; -moz-border-radius: 0 3px 3px 0;
}
.input-append :last-child.btn {
	border-radius: 0px 3px 3px 0px; -webkit-border-radius: 0 3px 3px 0; -moz-border-radius: 0 3px 3px 0;
}
.input-append.input-prepend input {
	border-radius: 0px; -webkit-border-radius: 0; -moz-border-radius: 0;
}
.input-append.input-prepend select {
	border-radius: 0px; -webkit-border-radius: 0; -moz-border-radius: 0;
}
.input-append.input-prepend .uneditable-input {
	border-radius: 0px; -webkit-border-radius: 0; -moz-border-radius: 0;
}
.input-append.input-prepend :first-child.add-on {
	border-radius: 3px 0px 0px 3px; margin-right: -1px; -webkit-border-radius: 3px 0 0 3px; -moz-border-radius: 3px 0 0 3px;
}
.input-append.input-prepend :first-child.btn {
	border-radius: 3px 0px 0px 3px; margin-right: -1px; -webkit-border-radius: 3px 0 0 3px; -moz-border-radius: 3px 0 0 3px;
}
.input-append.input-prepend :last-child.add-on {
	border-radius: 0px 3px 3px 0px; margin-left: -1px; -webkit-border-radius: 0 3px 3px 0; -moz-border-radius: 0 3px 3px 0;
}
.input-append.input-prepend :last-child.btn {
	border-radius: 0px 3px 3px 0px; margin-left: -1px; -webkit-border-radius: 0 3px 3px 0; -moz-border-radius: 0 3px 3px 0;
}
.search-query {
	border-radius: 14px; padding-right: 14px; padding-left: 14px; margin-bottom: 0px; -webkit-border-radius: 14px; -moz-border-radius: 14px;
}
.form-search input {
	margin-bottom: 0px; display: inline-block;
}
.form-inline input {
	margin-bottom: 0px; display: inline-block;
}
.form-horizontal input {
	margin-bottom: 0px; display: inline-block;
}
.form-search textarea {
	margin-bottom: 0px; display: inline-block;
}
.form-inline textarea {
	margin-bottom: 0px; display: inline-block;
}
.form-horizontal textarea {
	margin-bottom: 0px; display: inline-block;
}
.form-search select {
	margin-bottom: 0px; display: inline-block;
}
.form-inline select {
	margin-bottom: 0px; display: inline-block;
}
.form-horizontal select {
	margin-bottom: 0px; display: inline-block;
}
.form-search .help-inline {
	margin-bottom: 0px; display: inline-block;
}
.form-inline .help-inline {
	margin-bottom: 0px; display: inline-block;
}
.form-horizontal .help-inline {
	margin-bottom: 0px; display: inline-block;
}
.form-search .uneditable-input {
	margin-bottom: 0px; display: inline-block;
}
.form-inline .uneditable-input {
	margin-bottom: 0px; display: inline-block;
}
.form-horizontal .uneditable-input {
	margin-bottom: 0px; display: inline-block;
}
.form-search .input-prepend {
	margin-bottom: 0px; display: inline-block;
}
.form-inline .input-prepend {
	margin-bottom: 0px; display: inline-block;
}
.form-horizontal .input-prepend {
	margin-bottom: 0px; display: inline-block;
}
.form-search .input-append {
	margin-bottom: 0px; display: inline-block;
}
.form-inline .input-append {
	margin-bottom: 0px; display: inline-block;
}
.form-horizontal .input-append {
	margin-bottom: 0px; display: inline-block;
}
.form-search .hide {
	display: none;
}
.form-inline .hide {
	display: none;
}
.form-horizontal .hide {
	display: none;
}
.form-search label {
	display: inline-block;
}
.form-inline label {
	display: inline-block;
}
.form-search .input-append {
	margin-bottom: 0px;
}
.form-inline .input-append {
	margin-bottom: 0px;
}
.form-search .input-prepend {
	margin-bottom: 0px;
}
.form-inline .input-prepend {
	margin-bottom: 0px;
}
.form-search .radio {
	padding-left: 0px; margin-bottom: 0px; vertical-align: middle;
}
.form-search .checkbox {
	padding-left: 0px; margin-bottom: 0px; vertical-align: middle;
}
.form-inline .radio {
	padding-left: 0px; margin-bottom: 0px; vertical-align: middle;
}
.form-inline .checkbox {
	padding-left: 0px; margin-bottom: 0px; vertical-align: middle;
}
.form-search .radio input[type='radio'] {
	margin-right: 3px; margin-left: 0px; float: left;
}
.form-search .checkbox input[type='checkbox'] {
	margin-right: 3px; margin-left: 0px; float: left;
}
.form-inline .radio input[type='radio'] {
	margin-right: 3px; margin-left: 0px; float: left;
}
.form-inline .checkbox input[type='checkbox'] {
	margin-right: 3px; margin-left: 0px; float: left;
}
.control-group {
	margin-bottom: 9px;
}
legend + .control-group {
	margin-top: 18px; -webkit-margin-top-collapse: separate;
}
.form-horizontal .control-group {
	margin-bottom: 18px;
}
.form-horizontal .control-group::before {
	display: table; content: "";
}
.form-horizontal .control-group::after {
	display: table; content: "";
}
.form-horizontal .control-group::after {
	clear: both;
}
.form-horizontal .control-label {
	width: 140px; text-align: right; padding-top: 5px; float: left;
}
.form-horizontal .controls {
	margin-left: 160px;
}
.form-horizontal :first-child.controls {
	
}
.form-horizontal .help-block {
	margin-top: 9px; margin-bottom: 0px;
}
.form-horizontal .form-actions {
	padding-left: 160px;
}
table {
	border-collapse: collapse; max-width: 100%; border-spacing: 0; background-color: transparent;
}
.table {
	width: 100%; margin-bottom: 18px;
}
.table th {
	padding: 8px; text-align: left; line-height: 18px; vertical-align: top; border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.table td {
	padding: 8px; text-align: left; line-height: 18px; vertical-align: top; border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.table th {
	font-weight: bold;
}
.table thead th {
	vertical-align: bottom;
}
.table caption + thead tr:first-child th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table caption + thead tr:first-child td {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table colgroup + thead tr:first-child th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table colgroup + thead tr:first-child td {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table thead:first-child tr:first-child th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table thead:first-child tr:first-child td {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table tbody + tbody {
	border-top-color: rgb(221, 221, 221); border-top-width: 2px; border-top-style: solid;
}
.table-condensed th {
	padding: 4px 5px;
}
.table-condensed td {
	padding: 4px 5px;
}
.table-bordered {
	border-width: 1px 1px 1px 0px; border-style: solid solid solid none; border-color: rgb(221, 221, 221) rgb(221, 221, 221) rgb(221, 221, 221) currentColor; border-radius: 4px; border-image: none; border-collapse: separate; -webkit-border-radius: 4px; -moz-border-radius: 4px;
}
.table-bordered th {
	border-left-color: rgb(221, 221, 221); border-left-width: 1px; border-left-style: solid;
}
.table-bordered td {
	border-left-color: rgb(221, 221, 221); border-left-width: 1px; border-left-style: solid;
}
.table-bordered caption + thead tr:first-child th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table-bordered caption + tbody tr:first-child th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table-bordered caption + tbody tr:first-child td {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table-bordered colgroup + thead tr:first-child th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table-bordered colgroup + tbody tr:first-child th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table-bordered colgroup + tbody tr:first-child td {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table-bordered thead:first-child tr:first-child th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table-bordered tbody:first-child tr:first-child th {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table-bordered tbody:first-child tr:first-child td {
	border-top-color: currentColor; border-top-width: 0px; border-top-style: none;
}
.table-bordered thead:first-child tr:first-child th:first-child {
	border-top-left-radius: 4px; -webkit-border-top-left-radius: 4px; -moz-border-radius-topleft: 4px;
}
.table-bordered tbody:first-child tr:first-child td:first-child {
	border-top-left-radius: 4px; -webkit-border-top-left-radius: 4px; -moz-border-radius-topleft: 4px;
}
.table-bordered thead:first-child tr:first-child th:last-child {
	border-top-right-radius: 4px; -webkit-border-top-right-radius: 4px; -moz-border-radius-topright: 4px;
}
.table-bordered tbody:first-child tr:first-child td:last-child {
	border-top-right-radius: 4px; -webkit-border-top-right-radius: 4px; -moz-border-radius-topright: 4px;
}
.table-bordered thead:last-child tr:last-child th:first-child {
	border-radius: 0px 0px 0px 4px; -webkit-border-radius: 0 0 0 4px; -moz-border-radius: 0 0 0 4px; -webkit-border-bottom-left-radius: 4px; -moz-border-radius-bottomleft: 4px;
}
.table-bordered tbody:last-child tr:last-child td:first-child {
	border-radius: 0px 0px 0px 4px; -webkit-border-radius: 0 0 0 4px; -moz-border-radius: 0 0 0 4px; -webkit-border-bottom-left-radius: 4px; -moz-border-radius-bottomleft: 4px;
}
.table-bordered thead:last-child tr:last-child th:last-child {
	border-bottom-right-radius: 4px; -webkit-border-bottom-right-radius: 4px; -moz-border-radius-bottomright: 4px;
}
.table-bordered tbody:last-child tr:last-child td:last-child {
	border-bottom-right-radius: 4px; -webkit-border-bottom-right-radius: 4px; -moz-border-radius-bottomright: 4px;
}
.table-striped tbody tr:nth-child(2n+1) td {
	background-color: rgb(249, 249, 249);
}
.table-striped tbody tr:nth-child(2n+1) th {
	background-color: rgb(249, 249, 249);
}
.table tbody tr:hover td {
	background-color: rgb(245, 245, 245);
}
.table tbody tr:hover th {
	background-color: rgb(245, 245, 245);
}
table .span1 {
	width: 44px; margin-left: 0px; float: none;
}
table .span2 {
	width: 124px; margin-left: 0px; float: none;
}
table .span3 {
	width: 204px; margin-left: 0px; float: none;
}
table .span4 {
	width: 284px; margin-left: 0px; float: none;
}
table .span5 {
	width: 364px; margin-left: 0px; float: none;
}
table .span6 {
	width: 444px; margin-left: 0px; float: none;
}
table .span7 {
	width: 524px; margin-left: 0px; float: none;
}
table .span8 {
	width: 604px; margin-left: 0px; float: none;
}
table .span9 {
	width: 684px; margin-left: 0px; float: none;
}
table .span10 {
	width: 764px; margin-left: 0px; float: none;
}
table .span11 {
	width: 844px; margin-left: 0px; float: none;
}
table .span12 {
	width: 924px; margin-left: 0px; float: none;
}
table .span13 {
	width: 1004px; margin-left: 0px; float: none;
}
table .span14 {
	width: 1084px; margin-left: 0px; float: none;
}
table .span15 {
	width: 1164px; margin-left: 0px; float: none;
}
table .span16 {
	width: 1244px; margin-left: 0px; float: none;
}
table .span17 {
	width: 1324px; margin-left: 0px; float: none;
}
table .span18 {
	width: 1404px; margin-left: 0px; float: none;
}
table .span19 {
	width: 1484px; margin-left: 0px; float: none;
}
table .span20 {
	width: 1564px; margin-left: 0px; float: none;
}
table .span21 {
	width: 1644px; margin-left: 0px; float: none;
}
table .span22 {
	width: 1724px; margin-left: 0px; float: none;
}
table .span23 {
	width: 1804px; margin-left: 0px; float: none;
}
table .span24 {
	width: 1884px; margin-left: 0px; float: none;
}
[class^='icon-'] {
	background-position: 14px 14px; width: 14px; height: 14px; line-height: 14px; vertical-align: text-top; display: inline-block; background-image: url("../img/glyphicons-halflings.png"); background-repeat: no-repeat;
}
[class*=' icon-'] {
	background-position: 14px 14px; width: 14px; height: 14px; line-height: 14px; vertical-align: text-top; display: inline-block; background-image: url("../img/glyphicons-halflings.png"); background-repeat: no-repeat;
}
[class^='icon-']:last-child {
	
}
[class*=' icon-']:last-child {
	
}
.icon-white {
	background-image: url("../img/glyphicons-halflings-white.png");
}
.icon-glass {
	background-position: 0px 0px;
}
.icon-music {
	background-position: -24px 0px;
}
.icon-search {
	background-position: -48px 0px;
}
.icon-envelope {
	background-position: -72px 0px;
}
.icon-heart {
	background-position: -96px 1px;
}
.icon-star {
	background-position: -120px 0px;
}
.icon-star-empty {
	background-position: -144px 0px;
}
.icon-user {
	background-position: -168px 0px;
}
.icon-film {
	background-position: -192px 0px;
}
.icon-th-large {
	background-position: -216px 0px;
}
.icon-th {
	background-position: -240px 0px;
}
.icon-th-list {
	background-position: -264px 0px;
}
.icon-ok {
	background-position: -288px 0px;
}
.icon-remove {
	background-position: -312px 0px;
}
.icon-zoom-in {
	background-position: -336px 0px;
}
.icon-zoom-out {
	background-position: -360px 0px;
}
.icon-off {
	background-position: -384px 0px;
}
.icon-signal {
	background-position: -408px 0px;
}
.icon-cog {
	background-position: -432px 1px;
}
.icon-trash {
	background-position: -456px 0px;
}
.icon-home {
	background-position: 0px -24px;
}
.icon-file {
	background-position: -24px -24px;
}
.icon-time {
	background-position: -48px -24px;
}
.icon-road {
	background-position: -72px -24px;
}
.icon-download-alt {
	background-position: -96px -24px;
}
.icon-download {
	background-position: -120px -24px;
}
.icon-upload {
	background-position: -144px -24px;
}
.icon-inbox {
	background-position: -168px -24px;
}
.icon-play-circle {
	background-position: -192px -24px;
}
.icon-repeat {
	background-position: -216px -24px;
}
.icon-refresh {
	background-position: -240px -24px;
}
.icon-list-alt {
	background-position: -264px -24px;
}
.icon-lock {
	background-position: -287px -24px;
}
.icon-flag {
	background-position: -312px -24px;
}
.icon-headphones {
	background-position: -336px -24px;
}
.icon-volume-off {
	background-position: -360px -24px;
}
.icon-volume-down {
	background-position: -384px -24px;
}
.icon-volume-up {
	background-position: -408px -24px;
}
.icon-qrcode {
	background-position: -432px -24px;
}
.icon-barcode {
	background-position: -456px -24px;
}
.icon-tag {
	background-position: 0px -48px;
}
.icon-tags {
	background-position: -25px -48px;
}
.icon-book {
	background-position: -48px -48px;
}
.icon-bookmark {
	background-position: -72px -48px;
}
.icon-print {
	background-position: -96px -48px;
}
.icon-camera {
	background-position: -120px -48px;
}
.icon-font {
	background-position: -144px -48px;
}
.icon-bold {
	background-position: -167px -48px;
}
.icon-italic {
	background-position: -192px -48px;
}
.icon-text-height {
	background-position: -216px -48px;
}
.icon-text-width {
	background-position: -240px -48px;
}
.icon-align-left {
	background-position: -264px -48px;
}
.icon-align-center {
	background-position: -288px -48px;
}
.icon-align-right {
	background-position: -312px -48px;
}
.icon-align-justify {
	background-position: -336px -48px;
}
.icon-list {
	background-position: -360px -48px;
}
.icon-indent-left {
	background-position: -384px -48px;
}
.icon-indent-right {
	background-position: -408px -48px;
}
.icon-facetime-video {
	background-position: -432px -48px;
}
.icon-picture {
	background-position: -456px -48px;
}
.icon-pencil {
	background-position: 0px -72px;
}
.icon-map-marker {
	background-position: -24px -72px;
}
.icon-adjust {
	background-position: -48px -72px;
}
.icon-tint {
	background-position: -72px -72px;
}
.icon-edit {
	background-position: -96px -72px;
}
.icon-share {
	background-position: -120px -72px;
}
.icon-check {
	background-position: -144px -72px;
}
.icon-move {
	background-position: -168px -72px;
}
.icon-step-backward {
	background-position: -192px -72px;
}
.icon-fast-backward {
	background-position: -216px -72px;
}
.icon-backward {
	background-position: -240px -72px;
}
.icon-play {
	background-position: -264px -72px;
}
.icon-pause {
	background-position: -288px -72px;
}
.icon-stop {
	background-position: -312px -72px;
}
.icon-forward {
	background-position: -336px -72px;
}
.icon-fast-forward {
	background-position: -360px -72px;
}
.icon-step-forward {
	background-position: -384px -72px;
}
.icon-eject {
	background-position: -408px -72px;
}
.icon-chevron-left {
	background-position: -432px -72px;
}
.icon-chevron-right {
	background-position: -456px -72px;
}
.icon-plus-sign {
	background-position: 0px -96px;
}
.icon-minus-sign {
	background-position: -24px -96px;
}
.icon-remove-sign {
	background-position: -48px -96px;
}
.icon-ok-sign {
	background-position: -72px -96px;
}
.icon-question-sign {
	background-position: -96px -96px;
}
.icon-info-sign {
	background-position: -120px -95px;
}
.icon-screenshot {
	background-position: -144px -96px;
}
.icon-remove-circle {
	background-position: -168px -96px;
}
.icon-ok-circle {
	background-position: -192px -96px;
}
.icon-ban-circle {
	background-position: -216px -96px;
}
.icon-arrow-left {
	background-position: -240px -96px;
}
.icon-arrow-right {
	background-position: -264px -96px;
}
.icon-arrow-up {
	background-position: -289px -96px;
}
.icon-arrow-down {
	background-position: -312px -96px;
}
.icon-share-alt {
	background-position: -336px -96px;
}
.icon-resize-full {
	background-position: -360px -96px;
}
.icon-resize-small {
	background-position: -384px -96px;
}
.icon-plus {
	background-position: -408px -96px;
}
.icon-minus {
	background-position: -433px -96px;
}
.icon-asterisk {
	background-position: -456px -96px;
}
.icon-exclamation-sign {
	background-position: 0px -120px;
}
.icon-gift {
	background-position: -24px -120px;
}
.icon-leaf {
	background-position: -48px -120px;
}
.icon-fire {
	background-position: -72px -1px;
}
.icon-eye-open {
	background-position: -96px -120px;
}
.icon-eye-close {
	background-position: -120px -120px;
}
.icon-warning-sign {
	background-position: -144px -120px;
}
.icon-plane {
	background-position: -168px -120px;
}
.icon-calendar {
	background-position: -192px -120px;
}
.icon-random {
	background-position: -216px -120px;
}
.icon-comment {
	background-position: -240px -120px;
}
.icon-magnet {
	background-position: -264px -120px;
}
.icon-chevron-up {
	background-position: -288px -120px;
}
.icon-chevron-down {
	background-position: -313px -119px;
}
.icon-retweet {
	background-position: -336px -120px;
}
.icon-shopping-cart {
	background-position: -360px -120px;
}
.icon-folder-close {
	background-position: -384px -120px;
}
.icon-folder-open {
	background-position: -408px -120px;
}
.icon-resize-vertical {
	background-position: -432px -119px;
}
.icon-resize-horizontal {
	background-position: -456px -118px;
}
.icon-hdd {
	background-position: 0px -144px;
}
.icon-bullhorn {
	background-position: -24px -144px;
}
.icon-bell {
	background-position: -48px -144px;
}
.icon-certificate {
	background-position: -72px -144px;
}
.icon-thumbs-up {
	background-position: -96px -144px;
}
.icon-thumbs-down {
	background-position: -120px -144px;
}
.icon-hand-right {
	background-position: -144px -144px;
}
.icon-hand-left {
	background-position: -168px -144px;
}
.icon-hand-up {
	background-position: -192px -144px;
}
.icon-hand-down {
	background-position: -216px -144px;
}
.icon-circle-arrow-right {
	background-position: -240px -144px;
}
.icon-circle-arrow-left {
	background-position: -264px -144px;
}
.icon-circle-arrow-up {
	background-position: -288px -144px;
}
.icon-circle-arrow-down {
	background-position: -312px -144px;
}
.icon-globe {
	background-position: -336px -144px;
}
.icon-wrench {
	background-position: -360px -144px;
}
.icon-tasks {
	background-position: -384px -144px;
}
.icon-filter {
	background-position: -408px -144px;
}
.icon-briefcase {
	background-position: -432px -144px;
}
.icon-fullscreen {
	background-position: -456px -144px;
}
.dropup {
	position: relative;
}
.dropdown {
	position: relative;
}
.dropdown-toggle {
	
}
.dropdown-toggle:active {
	outline: 0px;
}
.open .dropdown-toggle {
	outline: 0px;
}
.caret {
	width: 0px; height: 0px; vertical-align: top; border-top-color: rgb(0, 0, 0); border-right-color: transparent; border-left-color: transparent; border-top-width: 4px; border-right-width: 4px; border-left-width: 4px; border-top-style: solid; border-right-style: solid; border-left-style: solid; display: inline-block; content: ""; opacity: 0.3;
}
.dropdown .caret {
	margin-top: 8px; margin-left: 2px;
}
.dropdown:hover .caret {
	opacity: 1;
}
.open .caret {
	opacity: 1;
}
.dropdown-menu {
	list-style: none; margin: 1px 0px 0px; padding: 4px 0px; border-radius: 5px; border: 1px solid rgba(0, 0, 0, 0.2); border-image: none; left: 0px; top: 100%; float: left; display: none; position: absolute; z-index: 1000; min-width: 160px; box-shadow: 0px 5px 10px rgba(0,0,0,0.2); background-clip: padding-box; background-color: rgb(255, 255, 255); -webkit-border-radius: 5px; -moz-border-radius: 5px; -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2); -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2); -webkit-background-clip: padding-box; -moz-background-clip: padding;
}
.pull-right.dropdown-menu {
	left: auto; right: 0px;
}
.dropdown-menu .divider {
	margin: 8px 1px; height: 1px; overflow: hidden; border-bottom-color: rgb(255, 255, 255); border-bottom-width: 1px; border-bottom-style: solid; background-color: rgb(229, 229, 229);
}
.dropdown-menu a {
	padding: 3px 15px; color: rgb(51, 51, 51); line-height: 18px; clear: both; font-weight: normal; display: block; white-space: nowrap;
}
.dropdown-menu li > a:hover {
	color: rgb(255, 255, 255); text-decoration: none; background-color: rgb(0, 136, 204);
}
.dropdown-menu .active > a {
	color: rgb(255, 255, 255); text-decoration: none; background-color: rgb(0, 136, 204);
}
.dropdown-menu .active > a:hover {
	color: rgb(255, 255, 255); text-decoration: none; background-color: rgb(0, 136, 204);
}
.open {
	
}
.open > .dropdown-menu {
	display: block;
}
.pull-right > .dropdown-menu {
	left: auto; right: 0px;
}
.dropup .caret {
	border-top-color: currentColor; border-bottom-color: rgb(0, 0, 0); border-top-width: 0px; border-bottom-width: 4px; border-top-style: none; border-bottom-style: solid; content: "\2191";
}
.navbar-fixed-bottom .dropdown .caret {
	border-top-color: currentColor; border-bottom-color: rgb(0, 0, 0); border-top-width: 0px; border-bottom-width: 4px; border-top-style: none; border-bottom-style: solid; content: "\2191";
}
.dropup .dropdown-menu {
	top: auto; bottom: 100%; margin-bottom: 1px;
}
.navbar-fixed-bottom .dropdown .dropdown-menu {
	top: auto; bottom: 100%; margin-bottom: 1px;
}
.typeahead {
	border-radius: 4px; margin-top: 2px; -webkit-border-radius: 4px; -moz-border-radius: 4px;
}
.well {
	padding: 19px; border-radius: 4px; border: 1px solid rgba(0, 0, 0, 0.05); border-image: none; margin-bottom: 20px; min-height: 20px; box-shadow: inset 0px 1px 1px rgba(0,0,0,0.05); background-color: rgb(245, 245, 245); -webkit-border-radius: 4px; -moz-border-radius: 4px; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05); -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}
.well blockquote {
	border-color: rgba(0, 0, 0, 0.15);
}
.well-large {
	padding: 24px; border-radius: 6px; -webkit-border-radius: 6px; -moz-border-radius: 6px;
}
.well-small {
	padding: 9px; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px;
}
.fade {
	transition:opacity 0.15s linear; opacity: 0; -webkit-transition: opacity 0.15s linear; -moz-transition: opacity 0.15s linear; -o-transition: opacity 0.15s linear;
}
.in.fade {
	opacity: 1;
}
.collapse {
	transition:height 0.35s; height: 0px; overflow: hidden; position: relative; -webkit-transition: height 0.35s ease; -moz-transition: height 0.35s ease; -o-transition: height 0.35s ease;
}
.in.collapse {
	height: auto;
}
.close {
	color: rgb(0, 0, 0); line-height: 18px; font-size: 20px; font-weight: bold; float: right; opacity: 0.2; text-shadow: 0px 1px 0px #ffffff;
}
.close:hover {
	color: rgb(0, 0, 0); text-decoration: none; cursor: pointer; opacity: 0.4;
}
button.close {
	background: none; padding: 0px; border: 0px currentColor; border-image: none; cursor: pointer; -webkit-appearance: none;
}
.btn {
	border-width: 1px; border-style: solid; border-color: rgb(230, 230, 230) rgb(230, 230, 230) rgb(179, 179, 179); padding: 4px 10px; border-radius: 4px; border-image: none; text-align: center; color: rgb(51, 51, 51); line-height: 18px; font-size: 13px; margin-bottom: 0px; vertical-align: middle; display: inline-block; cursor: pointer; box-shadow: inset 0px 1px 0px rgba(255,255,255,0.2), 0px 1px 2px rgba(0,0,0,0.05); text-shadow: 0px 1px 1px rgba(255,255,255,0.75); background-image: -ms-linear-gradient(rgb(255, 255, 255), rgb(230, 230, 230)); background-repeat: repeat-x; background-color: rgb(245, 245, 245); -webkit-border-radius: 4px; -moz-border-radius: 4px; -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05); -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.btn:hover {
	background-color: rgb(230, 230, 230);
}
.btn:active {
	background-color: rgb(230, 230, 230);
}
.active.btn {
	background-color: rgb(230, 230, 230);
}
.disabled.btn {
	background-color: rgb(230, 230, 230);
}
[disabled].btn {
	background-color: rgb(230, 230, 230);
}
.btn:active {
	
}
.active.btn {
	
}
:first-child.btn {
	
}
.btn:hover {
	background-position: 0px -15px; transition:background-position 0.1s linear; color: rgb(51, 51, 51); text-decoration: none; background-color: rgb(230, 230, 230); -webkit-transition: background-position 0.1s linear; -moz-transition: background-position 0.1s linear; -o-transition: background-position 0.1s linear;
}
.btn:focus {
	outline: rgb(51, 51, 51) dotted thin; outline-offset: -2px;
}
.active.btn {
	outline: 0px; box-shadow: inset 0px 2px 4px rgba(0,0,0,0.15), 0px 1px 2px rgba(0,0,0,0.05); background-image: none; background-color: rgb(230, 230, 230); -webkit-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05); -moz-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.btn:active {
	outline: 0px; box-shadow: inset 0px 2px 4px rgba(0,0,0,0.15), 0px 1px 2px rgba(0,0,0,0.05); background-image: none; background-color: rgb(230, 230, 230); -webkit-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05); -moz-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.disabled.btn {
	cursor: default; opacity: 0.65; box-shadow: none; background-image: none; background-color: rgb(230, 230, 230); -webkit-box-shadow: none; -moz-box-shadow: none;
}
[disabled].btn {
	cursor: default; opacity: 0.65; box-shadow: none; background-image: none; background-color: rgb(230, 230, 230); -webkit-box-shadow: none; -moz-box-shadow: none;
}
.btn-large {
	padding: 9px 14px; border-radius: 5px; line-height: normal; font-size: 15px; -webkit-border-radius: 5px; -moz-border-radius: 5px;
}
.btn-large [class^='icon-'] {
	margin-top: 1px;
}
.btn-small {
	padding: 5px 9px; line-height: 16px; font-size: 11px;
}
.btn-small [class^='icon-'] {
	margin-top: -1px;
}
.btn-mini {
	padding: 2px 6px; line-height: 14px; font-size: 11px;
}
.btn-primary {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.btn-primary:hover {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.btn-warning {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.btn-warning:hover {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.btn-danger {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.btn-danger:hover {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.btn-success {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.btn-success:hover {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.btn-info {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.btn-info:hover {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.btn-inverse {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.btn-inverse:hover {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.active.btn-primary {
	color: rgba(255, 255, 255, 0.75);
}
.active.btn-warning {
	color: rgba(255, 255, 255, 0.75);
}
.active.btn-danger {
	color: rgba(255, 255, 255, 0.75);
}
.active.btn-success {
	color: rgba(255, 255, 255, 0.75);
}
.active.btn-info {
	color: rgba(255, 255, 255, 0.75);
}
.active.btn-inverse {
	color: rgba(255, 255, 255, 0.75);
}
.btn {
	border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
}
.btn-primary {
	border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25); background-image: -ms-linear-gradient(rgb(0, 136, 204), rgb(0, 85, 204)); background-repeat: repeat-x; background-color: rgb(0, 116, 204);
}
.btn-primary:hover {
	background-color: rgb(0, 85, 204);
}
.btn-primary:active {
	background-color: rgb(0, 85, 204);
}
.active.btn-primary {
	background-color: rgb(0, 85, 204);
}
.disabled.btn-primary {
	background-color: rgb(0, 85, 204);
}
[disabled].btn-primary {
	background-color: rgb(0, 85, 204);
}
.btn-primary:active {
	
}
.active.btn-primary {
	
}
.btn-warning {
	border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25); background-image: -ms-linear-gradient(rgb(251, 180, 80), rgb(248, 148, 6)); background-repeat: repeat-x; background-color: rgb(250, 167, 50);
}
.btn-warning:hover {
	background-color: rgb(248, 148, 6);
}
.btn-warning:active {
	background-color: rgb(248, 148, 6);
}
.active.btn-warning {
	background-color: rgb(248, 148, 6);
}
.disabled.btn-warning {
	background-color: rgb(248, 148, 6);
}
[disabled].btn-warning {
	background-color: rgb(248, 148, 6);
}
.btn-warning:active {
	
}
.active.btn-warning {
	
}
.btn-danger {
	border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25); background-image: -ms-linear-gradient(rgb(238, 95, 91), rgb(189, 54, 47)); background-repeat: repeat-x; background-color: rgb(218, 79, 73);
}
.btn-danger:hover {
	background-color: rgb(189, 54, 47);
}
.btn-danger:active {
	background-color: rgb(189, 54, 47);
}
.active.btn-danger {
	background-color: rgb(189, 54, 47);
}
.disabled.btn-danger {
	background-color: rgb(189, 54, 47);
}
[disabled].btn-danger {
	background-color: rgb(189, 54, 47);
}
.btn-danger:active {
	
}
.active.btn-danger {
	
}
.btn-success {
	border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25); background-image: -ms-linear-gradient(rgb(98, 196, 98), rgb(81, 163, 81)); background-repeat: repeat-x; background-color: rgb(91, 183, 91);
}
.btn-success:hover {
	background-color: rgb(81, 163, 81);
}
.btn-success:active {
	background-color: rgb(81, 163, 81);
}
.active.btn-success {
	background-color: rgb(81, 163, 81);
}
.disabled.btn-success {
	background-color: rgb(81, 163, 81);
}
[disabled].btn-success {
	background-color: rgb(81, 163, 81);
}
.btn-success:active {
	
}
.active.btn-success {
	
}
.btn-info {
	border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25); background-image: -ms-linear-gradient(rgb(91, 192, 222), rgb(47, 150, 180)); background-repeat: repeat-x; background-color: rgb(73, 175, 205);
}
.btn-info:hover {
	background-color: rgb(47, 150, 180);
}
.btn-info:active {
	background-color: rgb(47, 150, 180);
}
.active.btn-info {
	background-color: rgb(47, 150, 180);
}
.disabled.btn-info {
	background-color: rgb(47, 150, 180);
}
[disabled].btn-info {
	background-color: rgb(47, 150, 180);
}
.btn-info:active {
	
}
.active.btn-info {
	
}
.btn-inverse {
	border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25); background-image: -ms-linear-gradient(rgb(85, 85, 85), rgb(34, 34, 34)); background-repeat: repeat-x; background-color: rgb(65, 65, 65);
}
.btn-inverse:hover {
	background-color: rgb(34, 34, 34);
}
.btn-inverse:active {
	background-color: rgb(34, 34, 34);
}
.active.btn-inverse {
	background-color: rgb(34, 34, 34);
}
.disabled.btn-inverse {
	background-color: rgb(34, 34, 34);
}
[disabled].btn-inverse {
	background-color: rgb(34, 34, 34);
}
.btn-inverse:active {
	
}
.active.btn-inverse {
	
}
button.btn {
	
}
input[type='submit'].btn {
	
}
button.btn-large.btn {
	
}
input[type='submit'].btn-large.btn {
	
}
button.btn-small.btn {
	
}
input[type='submit'].btn-small.btn {
	
}
button.btn-mini.btn {
	
}
input[type='submit'].btn-mini.btn {
	
}
.btn-group {
	position: relative;
}
.btn-group::before {
	display: table; content: "";
}
.btn-group::after {
	display: table; content: "";
}
.btn-group::after {
	clear: both;
}
:first-child.btn-group {
	
}
.btn-group + .btn-group {
	margin-left: 5px;
}
.btn-toolbar {
	margin-top: 9px; margin-bottom: 9px;
}
.btn-toolbar .btn-group {
	display: inline-block;
}
.btn-group > .btn {
	border-radius: 0px; margin-left: -1px; float: left; position: relative; -webkit-border-radius: 0; -moz-border-radius: 0;
}
.btn-group > :first-child.btn {
	margin-left: 0px; border-top-left-radius: 4px; border-bottom-left-radius: 4px; -webkit-border-top-left-radius: 4px; -moz-border-radius-topleft: 4px; -webkit-border-bottom-left-radius: 4px; -moz-border-radius-bottomleft: 4px;
}
.btn-group > :last-child.btn {
	border-top-right-radius: 4px; border-bottom-right-radius: 4px; -webkit-border-top-right-radius: 4px; -moz-border-radius-topright: 4px; -webkit-border-bottom-right-radius: 4px; -moz-border-radius-bottomright: 4px;
}
.btn-group > .dropdown-toggle {
	border-top-right-radius: 4px; border-bottom-right-radius: 4px; -webkit-border-top-right-radius: 4px; -moz-border-radius-topright: 4px; -webkit-border-bottom-right-radius: 4px; -moz-border-radius-bottomright: 4px;
}
.btn-group > :first-child.large.btn {
	margin-left: 0px; border-top-left-radius: 6px; border-bottom-left-radius: 6px; -webkit-border-top-left-radius: 6px; -moz-border-radius-topleft: 6px; -webkit-border-bottom-left-radius: 6px; -moz-border-radius-bottomleft: 6px;
}
.btn-group > :last-child.large.btn {
	border-top-right-radius: 6px; border-bottom-right-radius: 6px; -webkit-border-top-right-radius: 6px; -moz-border-radius-topright: 6px; -webkit-border-bottom-right-radius: 6px; -moz-border-radius-bottomright: 6px;
}
.btn-group > .dropdown-toggle.large {
	border-top-right-radius: 6px; border-bottom-right-radius: 6px; -webkit-border-top-right-radius: 6px; -moz-border-radius-topright: 6px; -webkit-border-bottom-right-radius: 6px; -moz-border-radius-bottomright: 6px;
}
.btn-group > .btn:hover {
	z-index: 2;
}
.btn-group > .btn:focus {
	z-index: 2;
}
.btn-group > .btn:active {
	z-index: 2;
}
.btn-group > .active.btn {
	z-index: 2;
}
.btn-group .dropdown-toggle:active {
	outline: 0px;
}
.open.btn-group .dropdown-toggle {
	outline: 0px;
}
.btn-group > .dropdown-toggle {
	padding-right: 8px; padding-left: 8px; box-shadow: inset 1px 0px 0px rgba(255,255,255,0.125), inset 0px 1px 0px rgba(255,255,255,0.2), 0px 1px 2px rgba(0,0,0,0.05); -webkit-box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.125), inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05); -moz-box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.125), inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.btn-group > .dropdown-toggle.btn-mini {
	padding-right: 5px; padding-left: 5px;
}
.btn-group > .dropdown-toggle.btn-small {
	
}
.btn-group > .dropdown-toggle.btn-large {
	padding-right: 12px; padding-left: 12px;
}
.open.btn-group .dropdown-toggle {
	box-shadow: inset 0px 2px 4px rgba(0,0,0,0.15), 0px 1px 2px rgba(0,0,0,0.05); background-image: none; -webkit-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05); -moz-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.open.btn-group .dropdown-toggle.btn {
	background-color: rgb(230, 230, 230);
}
.open.btn-group .dropdown-toggle.btn-primary {
	background-color: rgb(0, 85, 204);
}
.open.btn-group .dropdown-toggle.btn-warning {
	background-color: rgb(248, 148, 6);
}
.open.btn-group .dropdown-toggle.btn-danger {
	background-color: rgb(189, 54, 47);
}
.open.btn-group .dropdown-toggle.btn-success {
	background-color: rgb(81, 163, 81);
}
.open.btn-group .dropdown-toggle.btn-info {
	background-color: rgb(47, 150, 180);
}
.open.btn-group .dropdown-toggle.btn-inverse {
	background-color: rgb(34, 34, 34);
}
.btn .caret {
	margin-top: 7px; margin-left: 0px;
}
.btn:hover .caret {
	opacity: 1;
}
.btn-group.open .caret {
	opacity: 1;
}
.btn-mini .caret {
	margin-top: 5px;
}
.btn-small .caret {
	margin-top: 6px;
}
.btn-large .caret {
	margin-top: 6px; border-top-width: 5px; border-right-width: 5px; border-left-width: 5px;
}
.dropup .btn-large .caret {
	border-top-color: currentColor; border-bottom-color: rgb(0, 0, 0); border-top-width: 0px; border-bottom-width: 5px; border-top-style: none; border-bottom-style: solid;
}
.btn-primary .caret {
	border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255); opacity: 0.75;
}
.btn-warning .caret {
	border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255); opacity: 0.75;
}
.btn-danger .caret {
	border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255); opacity: 0.75;
}
.btn-info .caret {
	border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255); opacity: 0.75;
}
.btn-success .caret {
	border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255); opacity: 0.75;
}
.btn-inverse .caret {
	border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255); opacity: 0.75;
}
.alert {
	padding: 8px 35px 8px 14px; border-radius: 4px; border: 1px solid rgb(251, 238, 213); border-image: none; color: rgb(192, 152, 83); margin-bottom: 18px; text-shadow: 0px 1px 0px rgba(255,255,255,0.5); background-color: rgb(252, 248, 227); -webkit-border-radius: 4px; -moz-border-radius: 4px;
}
.alert-heading {
	color: inherit;
}
.alert .close {
	top: -2px; right: -21px; line-height: 18px; position: relative;
}
.alert-success {
	border-color: rgb(214, 233, 198); color: rgb(70, 136, 71); background-color: rgb(223, 240, 216);
}
.alert-danger {
	border-color: rgb(238, 211, 215); color: rgb(185, 74, 72); background-color: rgb(242, 222, 222);
}
.alert-error {
	border-color: rgb(238, 211, 215); color: rgb(185, 74, 72); background-color: rgb(242, 222, 222);
}
.alert-info {
	border-color: rgb(188, 232, 241); color: rgb(58, 135, 173); background-color: rgb(217, 237, 247);
}
.alert-block {
	padding-top: 14px; padding-bottom: 14px;
}
.alert-block > p {
	margin-bottom: 0px;
}
.alert-block > ul {
	margin-bottom: 0px;
}
.alert-block p + p {
	margin-top: 5px;
}
.nav {
	list-style: none; margin-bottom: 18px; margin-left: 0px;
}
.nav > li > a {
	display: block;
}
.nav > li > a:hover {
	text-decoration: none; background-color: rgb(238, 238, 238);
}
.nav > .pull-right {
	float: right;
}
.nav .nav-header {
	padding: 3px 15px; color: rgb(153, 153, 153); text-transform: uppercase; line-height: 18px; font-size: 11px; font-weight: bold; display: block; text-shadow: 0px 1px 0px rgba(255,255,255,0.5);
}
.nav li + .nav-header {
	margin-top: 9px;
}
.nav-list {
	padding-right: 15px; padding-left: 15px; margin-bottom: 0px;
}
.nav-list > li > a {
	margin-right: -15px; margin-left: -15px; text-shadow: 0px 1px 0px rgba(255,255,255,0.5);
}
.nav-list .nav-header {
	margin-right: -15px; margin-left: -15px; text-shadow: 0px 1px 0px rgba(255,255,255,0.5);
}
.nav-list > li > a {
	padding: 3px 15px;
}
.nav-list > .active > a {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.2); background-color: rgb(0, 136, 204);
}
.nav-list > .active > a:hover {
	color: rgb(255, 255, 255); text-shadow: 0px -1px 0px rgba(0,0,0,0.2); background-color: rgb(0, 136, 204);
}
.nav-list [class^='icon-'] {
	margin-right: 2px;
}
.nav-list .divider {
	margin: 8px 1px; height: 1px; overflow: hidden; border-bottom-color: rgb(255, 255, 255); border-bottom-width: 1px; border-bottom-style: solid; background-color: rgb(229, 229, 229);
}
.nav-tabs {
	
}
.nav-pills {
	
}
.nav-tabs::before {
	display: table; content: "";
}
.nav-pills::before {
	display: table; content: "";
}
.nav-tabs::after {
	display: table; content: "";
}
.nav-pills::after {
	display: table; content: "";
}
.nav-tabs::after {
	clear: both;
}
.nav-pills::after {
	clear: both;
}
.nav-tabs > li {
	float: left;
}
.nav-pills > li {
	float: left;
}
.nav-tabs > li > a {
	line-height: 14px; padding-right: 12px; padding-left: 12px; margin-right: 2px;
}
.nav-pills > li > a {
	line-height: 14px; padding-right: 12px; padding-left: 12px; margin-right: 2px;
}
.nav-tabs {
	border-bottom-color: rgb(221, 221, 221); border-bottom-width: 1px; border-bottom-style: solid;
}
.nav-tabs > li {
	margin-bottom: -1px;
}
.nav-tabs > li > a {
	border-radius: 4px 4px 0px 0px; border: 1px solid transparent; border-image: none; line-height: 18px; padding-top: 8px; padding-bottom: 8px; -webkit-border-radius: 4px 4px 0 0; -moz-border-radius: 4px 4px 0 0;
}
.nav-tabs > li > a:hover {
	border-color: rgb(238, 238, 238) rgb(238, 238, 238) rgb(221, 221, 221);
}
.nav-tabs > .active > a {
	border-width: 1px; border-style: solid; border-color: rgb(221, 221, 221) rgb(221, 221, 221) transparent; border-image: none; color: rgb(85, 85, 85); cursor: default; background-color: rgb(255, 255, 255);
}
.nav-tabs > .active > a:hover {
	border-width: 1px; border-style: solid; border-color: rgb(221, 221, 221) rgb(221, 221, 221) transparent; border-image: none; color: rgb(85, 85, 85); cursor: default; background-color: rgb(255, 255, 255);
}
.nav-pills > li > a {
	border-radius: 5px; padding-top: 8px; padding-bottom: 8px; margin-top: 2px; margin-bottom: 2px; -webkit-border-radius: 5px; -moz-border-radius: 5px;
}
.nav-pills > .active > a {
	color: rgb(255, 255, 255); background-color: rgb(0, 136, 204);
}
.nav-pills > .active > a:hover {
	color: rgb(255, 255, 255); background-color: rgb(0, 136, 204);
}
.nav-stacked > li {
	float: none;
}
.nav-stacked > li > a {
	margin-right: 0px;
}
.nav-stacked.nav-tabs {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.nav-stacked.nav-tabs > li > a {
	border-radius: 0px; border: 1px solid rgb(221, 221, 221); border-image: none; -webkit-border-radius: 0; -moz-border-radius: 0;
}
.nav-stacked.nav-tabs > li:first-child > a {
	border-radius: 4px 4px 0px 0px; -webkit-border-radius: 4px 4px 0 0; -moz-border-radius: 4px 4px 0 0;
}
.nav-stacked.nav-tabs > li:last-child > a {
	border-radius: 0px 0px 4px 4px; -webkit-border-radius: 0 0 4px 4px; -moz-border-radius: 0 0 4px 4px;
}
.nav-stacked.nav-tabs > li > a:hover {
	border-color: rgb(221, 221, 221); z-index: 2;
}
.nav-stacked.nav-pills > li > a {
	margin-bottom: 3px;
}
.nav-stacked.nav-pills > li:last-child > a {
	margin-bottom: 1px;
}
.nav-tabs .dropdown-menu {
	border-radius: 0px 0px 5px 5px; -webkit-border-radius: 0 0 5px 5px; -moz-border-radius: 0 0 5px 5px;
}
.nav-pills .dropdown-menu {
	border-radius: 4px; -webkit-border-radius: 4px; -moz-border-radius: 4px;
}
.nav-tabs .dropdown-toggle .caret {
	margin-top: 6px; border-top-color: rgb(0, 136, 204); border-bottom-color: rgb(0, 136, 204);
}
.nav-pills .dropdown-toggle .caret {
	margin-top: 6px; border-top-color: rgb(0, 136, 204); border-bottom-color: rgb(0, 136, 204);
}
.nav-tabs .dropdown-toggle:hover .caret {
	border-top-color: rgb(0, 85, 128); border-bottom-color: rgb(0, 85, 128);
}
.nav-pills .dropdown-toggle:hover .caret {
	border-top-color: rgb(0, 85, 128); border-bottom-color: rgb(0, 85, 128);
}
.nav-tabs .active .dropdown-toggle .caret {
	border-top-color: rgb(51, 51, 51); border-bottom-color: rgb(51, 51, 51);
}
.nav-pills .active .dropdown-toggle .caret {
	border-top-color: rgb(51, 51, 51); border-bottom-color: rgb(51, 51, 51);
}
.nav > .active.dropdown > a:hover {
	color: rgb(0, 0, 0); cursor: pointer;
}
.nav-tabs .open .dropdown-toggle {
	border-color: rgb(153, 153, 153); color: rgb(255, 255, 255); background-color: rgb(153, 153, 153);
}
.nav-pills .open .dropdown-toggle {
	border-color: rgb(153, 153, 153); color: rgb(255, 255, 255); background-color: rgb(153, 153, 153);
}
.nav > li.active.open.dropdown > a:hover {
	border-color: rgb(153, 153, 153); color: rgb(255, 255, 255); background-color: rgb(153, 153, 153);
}
.nav li.open.dropdown .caret {
	border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255); opacity: 1;
}
.nav li.active.open.dropdown .caret {
	border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255); opacity: 1;
}
.nav li.open.dropdown a:hover .caret {
	border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255); opacity: 1;
}
.tabs-stacked .open > a:hover {
	border-color: rgb(153, 153, 153);
}
.tabbable {
	
}
.tabbable::before {
	display: table; content: "";
}
.tabbable::after {
	display: table; content: "";
}
.tabbable::after {
	clear: both;
}
.tab-content {
	overflow: auto;
}
.tabs-below > .nav-tabs {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.tabs-right > .nav-tabs {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.tabs-left > .nav-tabs {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.tab-content > .tab-pane {
	display: none;
}
.pill-content > .pill-pane {
	display: none;
}
.tab-content > .active {
	display: block;
}
.pill-content > .active {
	display: block;
}
.tabs-below > .nav-tabs {
	border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid;
}
.tabs-below > .nav-tabs > li {
	margin-top: -1px; margin-bottom: 0px;
}
.tabs-below > .nav-tabs > li > a {
	border-radius: 0px 0px 4px 4px; -webkit-border-radius: 0 0 4px 4px; -moz-border-radius: 0 0 4px 4px;
}
.tabs-below > .nav-tabs > li > a:hover {
	border-top-color: rgb(221, 221, 221); border-bottom-color: transparent;
}
.tabs-below > .nav-tabs > .active > a {
	border-color: transparent rgb(221, 221, 221) rgb(221, 221, 221);
}
.tabs-below > .nav-tabs > .active > a:hover {
	border-color: transparent rgb(221, 221, 221) rgb(221, 221, 221);
}
.tabs-left > .nav-tabs > li {
	float: none;
}
.tabs-right > .nav-tabs > li {
	float: none;
}
.tabs-left > .nav-tabs > li > a {
	margin-right: 0px; margin-bottom: 3px; min-width: 74px;
}
.tabs-right > .nav-tabs > li > a {
	margin-right: 0px; margin-bottom: 3px; min-width: 74px;
}
.tabs-left > .nav-tabs {
	margin-right: 19px; border-right-color: rgb(221, 221, 221); border-right-width: 1px; border-right-style: solid; float: left;
}
.tabs-left > .nav-tabs > li > a {
	border-radius: 4px 0px 0px 4px; margin-right: -1px; -webkit-border-radius: 4px 0 0 4px; -moz-border-radius: 4px 0 0 4px;
}
.tabs-left > .nav-tabs > li > a:hover {
	border-color: rgb(238, 238, 238) rgb(221, 221, 221) rgb(238, 238, 238) rgb(238, 238, 238);
}
.tabs-left > .nav-tabs .active > a {
	border-color: rgb(221, 221, 221) transparent rgb(221, 221, 221) rgb(221, 221, 221);
}
.tabs-left > .nav-tabs .active > a:hover {
	border-color: rgb(221, 221, 221) transparent rgb(221, 221, 221) rgb(221, 221, 221);
}
.tabs-right > .nav-tabs {
	margin-left: 19px; border-left-color: rgb(221, 221, 221); border-left-width: 1px; border-left-style: solid; float: right;
}
.tabs-right > .nav-tabs > li > a {
	border-radius: 0px 4px 4px 0px; margin-left: -1px; -webkit-border-radius: 0 4px 4px 0; -moz-border-radius: 0 4px 4px 0;
}
.tabs-right > .nav-tabs > li > a:hover {
	border-color: rgb(238, 238, 238) rgb(238, 238, 238) rgb(238, 238, 238) rgb(221, 221, 221);
}
.tabs-right > .nav-tabs .active > a {
	border-color: rgb(221, 221, 221) rgb(221, 221, 221) rgb(221, 221, 221) transparent;
}
.tabs-right > .nav-tabs .active > a:hover {
	border-color: rgb(221, 221, 221) rgb(221, 221, 221) rgb(221, 221, 221) transparent;
}
.navbar {
	overflow: visible; margin-bottom: 18px;
}
.navbar-inner {
	border-radius: 4px; padding-right: 20px; padding-left: 20px; min-height: 40px; box-shadow: 0px 1px 3px rgba(0,0,0,0.25), inset 0px -1px 0px rgba(0,0,0,0.1); background-image: -ms-linear-gradient(rgb(51, 51, 51), rgb(34, 34, 34)); background-repeat: repeat-x; background-color: rgb(44, 44, 44); -webkit-border-radius: 4px; -moz-border-radius: 4px; -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.25), inset 0 -1px 0 rgba(0, 0, 0, 0.1); -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.25), inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}
.navbar .container {
	width: auto;
}
.collapse.nav-collapse {
	height: auto;
}
.navbar {
	color: rgb(153, 153, 153);
}
.navbar .brand:hover {
	text-decoration: none;
}
.navbar .brand {
	padding: 8px 20px 12px; color: rgb(153, 153, 153); line-height: 1; font-size: 20px; font-weight: 200; margin-left: -20px; float: left; display: block;
}
.navbar .navbar-text {
	line-height: 40px; margin-bottom: 0px;
}
.navbar .navbar-link {
	color: rgb(153, 153, 153);
}
.navbar .navbar-link:hover {
	color: rgb(255, 255, 255);
}
.navbar .btn {
	margin-top: 5px;
}
.navbar .btn-group {
	margin-top: 5px;
}
.navbar .btn-group .btn {
	margin: 0px;
}
.navbar-form {
	margin-bottom: 0px;
}
.navbar-form::before {
	display: table; content: "";
}
.navbar-form::after {
	display: table; content: "";
}
.navbar-form::after {
	clear: both;
}
.navbar-form input {
	margin-top: 5px;
}
.navbar-form select {
	margin-top: 5px;
}
.navbar-form .radio {
	margin-top: 5px;
}
.navbar-form .checkbox {
	margin-top: 5px;
}
.navbar-form input {
	margin-bottom: 0px; display: inline-block;
}
.navbar-form select {
	margin-bottom: 0px; display: inline-block;
}
.navbar-form input[type='image'] {
	margin-top: 3px;
}
.navbar-form input[type='checkbox'] {
	margin-top: 3px;
}
.navbar-form input[type='radio'] {
	margin-top: 3px;
}
.navbar-form .input-append {
	margin-top: 6px; white-space: nowrap;
}
.navbar-form .input-prepend {
	margin-top: 6px; white-space: nowrap;
}
.navbar-form .input-append input {
	margin-top: 0px;
}
.navbar-form .input-prepend input {
	margin-top: 0px;
}
.navbar-search {
	margin-top: 6px; margin-bottom: 0px; float: left; position: relative;
}
.navbar-search .search-query {
	padding: 4px 9px; border: 1px solid rgb(21, 21, 21); transition:none; border-image: none; color: rgb(255, 255, 255); line-height: 1; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 13px; font-weight: normal; box-shadow: inset 0px 1px 2px rgba(0,0,0,0.1), 0px 1px 0px rgba(255,255,255,0.15); background-color: rgb(98, 98, 98); -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 1px 0 rgba(255, 255, 255, 0.15); -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 1px 0 rgba(255, 255, 255, 0.15); -webkit-transition: none; -moz-transition: none; -o-transition: none;
}
.navbar-search :-ms-input-placeholder.search-query {
	color: rgb(204, 204, 204);
}
.navbar-search .search-query:focus {
	padding: 5px 10px; outline: 0px; border: 0px currentColor; border-image: none; color: rgb(51, 51, 51); box-shadow: 0px 0px 3px rgba(0,0,0,0.15); text-shadow: 0px 1px 0px #ffffff; background-color: rgb(255, 255, 255); -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.15); -moz-box-shadow: 0 0 3px rgba(0, 0, 0, 0.15);
}
.navbar-search .focused.search-query {
	padding: 5px 10px; outline: 0px; border: 0px currentColor; border-image: none; color: rgb(51, 51, 51); box-shadow: 0px 0px 3px rgba(0,0,0,0.15); text-shadow: 0px 1px 0px #ffffff; background-color: rgb(255, 255, 255); -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.15); -moz-box-shadow: 0 0 3px rgba(0, 0, 0, 0.15);
}
.navbar-fixed-top {
	left: 0px; right: 0px; margin-bottom: 0px; position: fixed; z-index: 1030;
}
.navbar-fixed-bottom {
	left: 0px; right: 0px; margin-bottom: 0px; position: fixed; z-index: 1030;
}
.navbar-fixed-top .navbar-inner {
	border-radius: 0px; padding-right: 0px; padding-left: 0px; -webkit-border-radius: 0; -moz-border-radius: 0;
}
.navbar-fixed-bottom .navbar-inner {
	border-radius: 0px; padding-right: 0px; padding-left: 0px; -webkit-border-radius: 0; -moz-border-radius: 0;
}
.navbar-fixed-top .container {
	width: 940px;
}
.navbar-fixed-bottom .container {
	width: 940px;
}
.navbar-fixed-top {
	top: 0px;
}
.navbar-fixed-bottom {
	bottom: 0px;
}
.navbar .nav {
	margin: 0px 10px 0px 0px; left: 0px; float: left; display: block; position: relative;
}
.navbar .pull-right.nav {
	float: right;
}
.navbar .nav > li {
	float: left; display: block;
}
.navbar .nav > li > a {
	padding: 9px 10px 11px; color: rgb(153, 153, 153); line-height: 19px; text-decoration: none; float: none; text-shadow: 0px -1px 0px rgba(0,0,0,0.25);
}
.navbar .btn {
	margin: 5px 5px 6px; padding: 4px 10px; line-height: 18px; display: inline-block;
}
.navbar .btn-group {
	margin: 0px; padding: 5px 5px 6px;
}
.navbar .nav > li > a:hover {
	color: rgb(255, 255, 255); text-decoration: none; background-color: transparent;
}
.navbar .nav .active > a {
	color: rgb(255, 255, 255); text-decoration: none; background-color: rgb(34, 34, 34);
}
.navbar .nav .active > a:hover {
	color: rgb(255, 255, 255); text-decoration: none; background-color: rgb(34, 34, 34);
}
.navbar .divider-vertical {
	margin: 0px 9px; width: 1px; height: 40px; overflow: hidden; border-right-color: rgb(51, 51, 51); border-right-width: 1px; border-right-style: solid; background-color: rgb(34, 34, 34);
}
.navbar .pull-right.nav {
	margin-right: 0px; margin-left: 10px;
}
.navbar .btn-navbar {
	border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25); padding: 7px 10px; margin-right: 5px; margin-left: 5px; float: right; display: none; box-shadow: inset 0px 1px 0px rgba(255,255,255,0.1), 0px 1px 0px rgba(255,255,255,0.075); background-image: -ms-linear-gradient(rgb(51, 51, 51), rgb(34, 34, 34)); background-repeat: repeat-x; background-color: rgb(44, 44, 44); -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.075); -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.075);
}
.navbar .btn-navbar:hover {
	background-color: rgb(34, 34, 34);
}
.navbar .btn-navbar:active {
	background-color: rgb(34, 34, 34);
}
.navbar .active.btn-navbar {
	background-color: rgb(34, 34, 34);
}
.navbar .disabled.btn-navbar {
	background-color: rgb(34, 34, 34);
}
.navbar [disabled].btn-navbar {
	background-color: rgb(34, 34, 34);
}
.navbar .btn-navbar:active {
	
}
.navbar .active.btn-navbar {
	
}
.navbar .btn-navbar .icon-bar {
	border-radius: 1px; width: 18px; height: 2px; display: block; box-shadow: 0px 1px 0px rgba(0,0,0,0.25); background-color: rgb(245, 245, 245); -webkit-border-radius: 1px; -moz-border-radius: 1px; -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.25); -moz-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.25);
}
.btn-navbar .icon-bar + .icon-bar {
	margin-top: 3px;
}
.navbar .dropdown-menu::before {
	left: 9px; top: -7px; border-right-color: transparent; border-bottom-color: rgba(0, 0, 0, 0.2); border-left-color: transparent; border-right-width: 7px; border-bottom-width: 7px; border-left-width: 7px; border-right-style: solid; border-bottom-style: solid; border-left-style: solid; display: inline-block; position: absolute; content: "";
}
.navbar .dropdown-menu::after {
	left: 10px; top: -6px; border-right-color: transparent; border-bottom-color: rgb(255, 255, 255); border-left-color: transparent; border-right-width: 6px; border-bottom-width: 6px; border-left-width: 6px; border-right-style: solid; border-bottom-style: solid; border-left-style: solid; display: inline-block; position: absolute; content: "";
}
.navbar-fixed-bottom .dropdown-menu::before {
	top: auto; bottom: -7px; border-top-color: rgba(0, 0, 0, 0.2); border-bottom-color: currentColor; border-top-width: 7px; border-bottom-width: 0px; border-top-style: solid; border-bottom-style: none;
}
.navbar-fixed-bottom .dropdown-menu::after {
	top: auto; bottom: -6px; border-top-color: rgb(255, 255, 255); border-bottom-color: currentColor; border-top-width: 6px; border-bottom-width: 0px; border-top-style: solid; border-bottom-style: none;
}
.navbar .nav li.dropdown .dropdown-toggle .caret {
	border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255);
}
.navbar .nav li.open.dropdown .caret {
	border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255);
}
.navbar .nav li.active.dropdown .caret {
	opacity: 1;
}
.navbar .nav li.open.dropdown > .dropdown-toggle {
	background-color: transparent;
}
.navbar .nav li.active.dropdown > .dropdown-toggle {
	background-color: transparent;
}
.navbar .nav li.active.open.dropdown > .dropdown-toggle {
	background-color: transparent;
}
.navbar .nav li.active.dropdown > .dropdown-toggle:hover {
	color: rgb(255, 255, 255);
}
.navbar .pull-right .dropdown-menu {
	left: auto; right: 0px;
}
.navbar .pull-right.dropdown-menu {
	left: auto; right: 0px;
}
.navbar .pull-right .dropdown-menu::before {
	left: auto; right: 12px;
}
.navbar .pull-right.dropdown-menu::before {
	left: auto; right: 12px;
}
.navbar .pull-right .dropdown-menu::after {
	left: auto; right: 13px;
}
.navbar .pull-right.dropdown-menu::after {
	left: auto; right: 13px;
}
.breadcrumb {
	list-style: none; margin: 0px 0px 18px; padding: 7px 14px; border-radius: 3px; border: 1px solid rgb(221, 221, 221); border-image: none; box-shadow: inset 0px 1px 0px #ffffff; background-image: -ms-linear-gradient(rgb(255, 255, 255), rgb(245, 245, 245)); background-repeat: repeat-x; background-color: rgb(251, 251, 251); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: inset 0 1px 0 #ffffff; -moz-box-shadow: inset 0 1px 0 #ffffff;
}
.breadcrumb li {
	display: inline-block; text-shadow: 0px 1px 0px #ffffff;
}
.breadcrumb .divider {
	padding: 0px 5px; color: rgb(153, 153, 153);
}
.breadcrumb .active a {
	color: rgb(51, 51, 51);
}
.pagination {
	margin: 18px 0px; height: 36px;
}
.pagination ul {
	border-radius: 3px; margin-bottom: 0px; margin-left: 0px; display: inline-block; box-shadow: 0px 1px 2px rgba(0,0,0,0.05); -webkit-border-radius: 3px; -moz-border-radius: 3px; -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); -moz-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.pagination li {
	display: inline;
}
.pagination a {
	border-width: 1px 1px 1px 0px; border-style: solid; border-color: rgb(221, 221, 221); padding: 0px 14px; border-image: none; line-height: 34px; text-decoration: none; float: left;
}
.pagination a:hover {
	background-color: rgb(245, 245, 245);
}
.pagination .active a {
	background-color: rgb(245, 245, 245);
}
.pagination .active a {
	color: rgb(153, 153, 153); cursor: default;
}
.pagination .disabled span {
	color: rgb(153, 153, 153); cursor: default; background-color: transparent;
}
.pagination .disabled a {
	color: rgb(153, 153, 153); cursor: default; background-color: transparent;
}
.pagination .disabled a:hover {
	color: rgb(153, 153, 153); cursor: default; background-color: transparent;
}
.pagination li:first-child a {
	border-radius: 3px 0px 0px 3px; border-left-width: 1px; -webkit-border-radius: 3px 0 0 3px; -moz-border-radius: 3px 0 0 3px;
}
.pagination li:last-child a {
	border-radius: 0px 3px 3px 0px; -webkit-border-radius: 0 3px 3px 0; -moz-border-radius: 0 3px 3px 0;
}
.pagination-centered {
	text-align: center;
}
.pagination-right {
	text-align: right;
}
.pager {
	list-style: none; text-align: center; margin-bottom: 18px; margin-left: 0px;
}
.pager::before {
	display: table; content: "";
}
.pager::after {
	display: table; content: "";
}
.pager::after {
	clear: both;
}
.pager li {
	display: inline;
}
.pager a {
	padding: 5px 14px; border-radius: 15px; border: 1px solid rgb(221, 221, 221); border-image: none; display: inline-block; background-color: rgb(255, 255, 255); -webkit-border-radius: 15px; -moz-border-radius: 15px;
}
.pager a:hover {
	text-decoration: none; background-color: rgb(245, 245, 245);
}
.pager .next a {
	float: right;
}
.pager .previous a {
	float: left;
}
.pager .disabled a {
	color: rgb(153, 153, 153); cursor: default; background-color: rgb(255, 255, 255);
}
.pager .disabled a:hover {
	color: rgb(153, 153, 153); cursor: default; background-color: rgb(255, 255, 255);
}
.modal-open .dropdown-menu {
	z-index: 2050;
}
.modal-open .open.dropdown {
	
}
.modal-open .popover {
	z-index: 2060;
}
.modal-open .tooltip {
	z-index: 2070;
}
.modal-backdrop {
	left: 0px; top: 0px; right: 0px; bottom: 0px; position: fixed; z-index: 1040; background-color: rgb(0, 0, 0);
}
.fade.modal-backdrop {
	opacity: 0;
}
.modal-backdrop {
	opacity: 0.8;
}
.in.fade.modal-backdrop {
	opacity: 0.8;
}
.modal {
	margin: -250px 0px 0px -280px; border-radius: 2px; border: 1px solid rgba(0, 0, 0, 0.3); border-image: none; left: 50%; top: 50%; width: 560px; overflow: auto; position: fixed; z-index: 1050; box-shadow: 0px 3px 7px rgba(0,0,0,0.3); background-clip: padding-box; background-color: rgb(255, 255, 255); -webkit-border-radius: 2px; -moz-border-radius: 2px; -webkit-box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3); -moz-box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3); -webkit-background-clip: padding-box; -moz-background-clip: padding-box;
}
.fade.modal {
	transition:opacity 0.3s linear, top 0.3s ease-out; top: -25%; -webkit-transition: opacity 0.3s linear, top 0.3s ease-out; -moz-transition: opacity 0.3s linear, top 0.3s ease-out; -o-transition: opacity 0.3s linear, top 0.3s ease-out;
}
.in.fade.modal {
	top: 50%;
}
.modal-header {
	background: linear-gradient(rgba(248, 248, 248, 1) 0%, rgba(232, 232, 232, 1) 100%); padding: 9px 15px; border-bottom-color: rgb(205, 205, 205); border-bottom-width: 1px; border-bottom-style: solid;
}
.modal-header .close {
	margin-top: 2px;
}
.modal-body {
	background: rgb(247, 247, 247); padding: 15px; -ms-overflow-y: auto; max-height: 400px;
}
.modal-form {
	margin-bottom: 0px;
}
.modal-footer {
	padding: 14px 15px 15px; border-radius: 0px 0px 6px 6px; text-align: right; margin-bottom: 0px; border-top-color: rgb(221, 221, 221); border-top-width: 1px; border-top-style: solid; box-shadow: inset 0px 1px 0px #ffffff; background-color: rgb(245, 245, 245); -webkit-border-radius: 0 0 6px 6px; -moz-border-radius: 0 0 6px 6px; -webkit-box-shadow: inset 0 1px 0 #ffffff; -moz-box-shadow: inset 0 1px 0 #ffffff;
}
.modal-footer::before {
	display: table; content: "";
}
.modal-footer::after {
	display: table; content: "";
}
.modal-footer::after {
	clear: both;
}
.modal-footer .btn + .btn {
	margin-bottom: 0px; margin-left: 5px;
}
.modal-footer .btn-group .btn + .btn {
	margin-left: -1px;
}
.tooltip {
	padding: 5px; font-size: 11px; display: block; visibility: visible; position: absolute; z-index: 1020; opacity: 0;
}
.in.tooltip {
	opacity: 0.8;
}
.top.tooltip {
	margin-top: -2px;
}
.right.tooltip {
	margin-left: 2px;
}
.bottom.tooltip {
	margin-top: 2px;
}
.left.tooltip {
	margin-left: -2px;
}
.top.tooltip .tooltip-arrow {
	left: 50%; bottom: 0px; margin-left: -5px; border-top-color: rgb(0, 0, 0); border-right-color: transparent; border-left-color: transparent; border-top-width: 5px; border-right-width: 5px; border-left-width: 5px; border-top-style: solid; border-right-style: solid; border-left-style: solid;
}
.left.tooltip .tooltip-arrow {
	top: 50%; right: 0px; margin-top: -5px; border-top-color: transparent; border-bottom-color: transparent; border-left-color: rgb(0, 0, 0); border-top-width: 5px; border-bottom-width: 5px; border-left-width: 5px; border-top-style: solid; border-bottom-style: solid; border-left-style: solid;
}
.bottom.tooltip .tooltip-arrow {
	left: 50%; top: 0px; margin-left: -5px; border-right-color: transparent; border-bottom-color: rgb(0, 0, 0); border-left-color: transparent; border-right-width: 5px; border-bottom-width: 5px; border-left-width: 5px; border-right-style: solid; border-bottom-style: solid; border-left-style: solid;
}
.right.tooltip .tooltip-arrow {
	left: 0px; top: 50%; margin-top: -5px; border-top-color: transparent; border-right-color: rgb(0, 0, 0); border-bottom-color: transparent; border-top-width: 5px; border-right-width: 5px; border-bottom-width: 5px; border-top-style: solid; border-right-style: solid; border-bottom-style: solid;
}
.tooltip-inner {
	padding: 3px 8px; border-radius: 4px; text-align: center; color: rgb(255, 255, 255); text-decoration: none; max-width: 200px; background-color: rgb(0, 0, 0); -webkit-border-radius: 4px; -moz-border-radius: 4px;
}
.tooltip-arrow {
	width: 0px; height: 0px; position: absolute;
}
.popover {
	padding: 1px; border-radius: 2px; border: 1px solid rgba(0, 0, 0, 0.2); border-image: none; left: 0px; top: 0px; width: 236px; display: none; position: absolute; z-index: 1010; box-shadow: 0px 5px 10px rgba(0,0,0,0.2); background-clip: padding-box; background-color: rgb(247, 247, 247); -webkit-border-radius: 2px; -moz-border-radius: 2px; -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2); -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2); -webkit-background-clip: padding-box; -moz-background-clip: padding;
}
.top.popover {
	margin-bottom: 10px;
}
.right.popover {
	margin-left: 10px;
}
.bottom.popover {
	margin-top: 10px;
}
.left.popover {
	margin-right: 10px;
}
.popover-title {
	background: linear-gradient(rgba(248, 248, 248, 1) 0%, rgba(232, 232, 232, 1) 100%); margin: 0px; padding: 8px 14px; line-height: 18px; font-size: 14px; font-weight: normal; border-bottom-color: rgb(205, 205, 205); border-bottom-width: 1px; border-bottom-style: solid; box-shadow: 0px 1px 0px #fff; -webkit-box-shadow: 0 1px 0 #fff; -moz-box-shadow: 0 1px 0 #fff;
}
.popover-content {
	padding: 9px 14px;
}
.popover-content p {
	margin-bottom: 0px;
}
.popover-content ul {
	margin-bottom: 0px;
}
.popover-content ol {
	margin-bottom: 0px;
}
.popover .arrow {
	border-style: solid; border-color: transparent; width: 0px; height: 0px; display: inline-block; position: absolute;
}
.popover .arrow::after {
	border-style: solid; border-color: transparent; width: 0px; height: 0px; display: inline-block; position: absolute;
}
.popover .arrow::after {
	z-index: -1; content: "";
}
.top.popover .arrow {
	border-width: 10px 10px 0px; left: 50%; bottom: -10px; margin-left: -10px; border-top-color: rgb(247, 247, 247);
}
.top.popover .arrow::after {
	border-width: 11px 11px 0px; left: -11px; bottom: -1px; border-top-color: rgb(205, 205, 205);
}
.right.popover .arrow {
	border-width: 10px 10px 10px 0px; left: -10px; top: 50%; margin-top: -10px; border-right-color: rgb(247, 247, 247);
}
.right.popover .arrow::after {
	border-width: 11px 11px 11px 0px; left: -1px; bottom: -11px; border-right-color: rgb(205, 205, 205);
}
.bottom.popover .arrow {
	border-width: 0px 10px 10px; left: 50%; top: -10px; margin-left: -10px; border-bottom-color: rgb(247, 247, 247);
}
.bottom.popover .arrow::after {
	border-width: 0px 11px 11px; left: -11px; top: -1px; border-bottom-color: rgb(205, 205, 205);
}
.left.popover .arrow {
	border-width: 10px 0px 10px 10px; top: 50%; right: -10px; margin-top: -10px; border-left-color: rgb(247, 247, 247);
}
.left.popover .arrow::after {
	border-width: 11px 0px 11px 11px; right: -1px; bottom: -11px; border-left-color: rgb(205, 205, 205);
}
.thumbnails {
	list-style: none; margin-left: -20px;
}
.thumbnails::before {
	display: table; content: "";
}
.thumbnails::after {
	display: table; content: "";
}
.thumbnails::after {
	clear: both;
}
.row-fluid .thumbnails {
	margin-left: 0px;
}
.thumbnails > li {
	margin-bottom: 18px; margin-left: 20px; float: left;
}
.thumbnail {
	padding: 4px; border-radius: 4px; border: 1px solid rgb(221, 221, 221); border-image: none; line-height: 1; display: block; box-shadow: 0px 1px 1px rgba(0,0,0,0.075); -webkit-border-radius: 4px; -moz-border-radius: 4px; -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075); -moz-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075);
}
a.thumbnail:hover {
	border-color: rgb(0, 136, 204); box-shadow: 0px 1px 4px rgba(0,105,214,0.25); -webkit-box-shadow: 0 1px 4px rgba(0, 105, 214, 0.25); -moz-box-shadow: 0 1px 4px rgba(0, 105, 214, 0.25);
}
.thumbnail > img {
	margin-right: auto; margin-left: auto; display: block; max-width: 100%;
}
.thumbnail .caption {
	padding: 9px;
}
.label {
	color: rgb(255, 255, 255); line-height: 14px; font-size: 10.99px; font-weight: bold; vertical-align: baseline; white-space: nowrap; text-shadow: 0px -1px 0px rgba(0,0,0,0.25); background-color: rgb(153, 153, 153);
}
.badge {
	color: rgb(255, 255, 255); line-height: 14px; font-size: 10.99px; font-weight: bold; vertical-align: baseline; white-space: nowrap; text-shadow: 0px -1px 0px rgba(0,0,0,0.25); background-color: rgb(153, 153, 153);
}
.label {
	padding: 1px 4px 2px; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px;
}
.badge {
	padding: 1px 9px 2px; border-radius: 9px; -webkit-border-radius: 9px; -moz-border-radius: 9px;
}
a.label:hover {
	color: rgb(255, 255, 255); text-decoration: none; cursor: pointer;
}
a.badge:hover {
	color: rgb(255, 255, 255); text-decoration: none; cursor: pointer;
}
.label-important {
	background-color: rgb(185, 74, 72);
}
.badge-important {
	background-color: rgb(185, 74, 72);
}
[href].label-important {
	background-color: rgb(149, 59, 57);
}
[href].badge-important {
	background-color: rgb(149, 59, 57);
}
.label-warning {
	background-color: rgb(248, 148, 6);
}
.badge-warning {
	background-color: rgb(248, 148, 6);
}
[href].label-warning {
	background-color: rgb(198, 118, 5);
}
[href].badge-warning {
	background-color: rgb(198, 118, 5);
}
.label-success {
	background-color: rgb(70, 136, 71);
}
.badge-success {
	background-color: rgb(70, 136, 71);
}
[href].label-success {
	background-color: rgb(53, 102, 53);
}
[href].badge-success {
	background-color: rgb(53, 102, 53);
}
.label-info {
	background-color: rgb(58, 135, 173);
}
.badge-info {
	background-color: rgb(58, 135, 173);
}
[href].label-info {
	background-color: rgb(45, 105, 135);
}
[href].badge-info {
	background-color: rgb(45, 105, 135);
}
.label-inverse {
	background-color: rgb(51, 51, 51);
}
.badge-inverse {
	background-color: rgb(51, 51, 51);
}
[href].label-inverse {
	background-color: rgb(26, 26, 26);
}
[href].badge-inverse {
	background-color: rgb(26, 26, 26);
}
.progress {
	border-radius: 4px; height: 20px; overflow: hidden; margin-bottom: 20px; box-shadow: inset 0px 1px 2px rgba(0,0,0,0.1); background-image: linear-gradient(rgb(245, 245, 245), rgb(249, 249, 249)); background-repeat: repeat-x; background-color: rgb(247, 247, 247); -webkit-border-radius: 4px; -moz-border-radius: 4px; -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1); -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
.progress .bar {
	transition:width 0.6s; width: 0px; height: 100%; text-align: center; color: rgb(255, 255, 255); font-size: 12px; float: left; box-sizing: border-box; box-shadow: inset 0px -1px 0px rgba(0,0,0,0.15); text-shadow: 0px -1px 0px rgba(0,0,0,0.25); background-image: linear-gradient(rgb(20, 155, 223), rgb(4, 128, 190)); background-repeat: repeat-x; background-color: rgb(14, 144, 210); -webkit-box-sizing: border-box; -moz-box-sizing: border-box; -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15); -moz-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15); -webkit-transition: width 0.6s ease; -moz-transition: width 0.6s ease; -o-transition: width 0.6s ease;
}
.progress .bar + .bar {
	box-shadow: inset 1px 0px 0px rgba(0,0,0,0.15), inset 0px -1px 0px rgba(0,0,0,0.15); -webkit-box-shadow: inset 1px 0 0 rgba(0, 0, 0, 0.15), inset 0 -1px 0 rgba(0, 0, 0, 0.15); -moz-box-shadow: inset 1px 0 0 rgba(0, 0, 0, 0.15), inset 0 -1px 0 rgba(0, 0, 0, 0.15);
}
.progress-striped .bar {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-size: 40px 40px; background-color: rgb(20, 155, 223); -webkit-background-size: 40px 40px; -moz-background-size: 40px 40px; -o-background-size: 40px 40px;
}
.active.progress .bar {
	animation:progress-bar-stripes 2s linear infinite; -webkit-animation: progress-bar-stripes 2s linear infinite; -moz-animation: progress-bar-stripes 2s linear infinite; -o-animation: progress-bar-stripes 2s linear infinite;
}
.progress-danger .bar {
	background-image: linear-gradient(rgb(238, 95, 91), rgb(196, 60, 53)); background-repeat: repeat-x; background-color: rgb(221, 81, 76);
}
.progress .bar-danger {
	background-image: linear-gradient(rgb(238, 95, 91), rgb(196, 60, 53)); background-repeat: repeat-x; background-color: rgb(221, 81, 76);
}
.progress-striped.progress-danger .bar {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-color: rgb(238, 95, 91);
}
.progress-striped .bar-danger {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-color: rgb(238, 95, 91);
}
.progress-success .bar {
	background-image: linear-gradient(rgb(98, 196, 98), rgb(87, 169, 87)); background-repeat: repeat-x; background-color: rgb(94, 185, 94);
}
.progress .bar-success {
	background-image: linear-gradient(rgb(98, 196, 98), rgb(87, 169, 87)); background-repeat: repeat-x; background-color: rgb(94, 185, 94);
}
.progress-striped.progress-success .bar {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-color: rgb(98, 196, 98);
}
.progress-striped .bar-success {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-color: rgb(98, 196, 98);
}
.progress-info .bar {
	background-image: linear-gradient(rgb(91, 192, 222), rgb(51, 155, 185)); background-repeat: repeat-x; background-color: rgb(75, 177, 207);
}
.progress .bar-info {
	background-image: linear-gradient(rgb(91, 192, 222), rgb(51, 155, 185)); background-repeat: repeat-x; background-color: rgb(75, 177, 207);
}
.progress-striped.progress-info .bar {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-color: rgb(91, 192, 222);
}
.progress-striped .bar-info {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-color: rgb(91, 192, 222);
}
.progress-warning .bar {
	background-image: linear-gradient(rgb(251, 180, 80), rgb(248, 148, 6)); background-repeat: repeat-x; background-color: rgb(250, 167, 50);
}
.progress .bar-warning {
	background-image: linear-gradient(rgb(251, 180, 80), rgb(248, 148, 6)); background-repeat: repeat-x; background-color: rgb(250, 167, 50);
}
.progress-striped.progress-warning .bar {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-color: rgb(251, 180, 80);
}
.progress-striped .bar-warning {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-color: rgb(251, 180, 80);
}
.accordion {
	margin-bottom: 18px;
}
.accordion-group {
	border-radius: 4px; border: 1px solid rgb(229, 229, 229); border-image: none; margin-bottom: 2px; -webkit-border-radius: 4px; -moz-border-radius: 4px;
}
.accordion-heading {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.accordion-heading .accordion-toggle {
	padding: 8px 15px; display: block;
}
.accordion-toggle {
	cursor: pointer;
}
.accordion-inner {
	padding: 9px 15px; border-top-color: rgb(229, 229, 229); border-top-width: 1px; border-top-style: solid;
}
.carousel {
	line-height: 1; margin-bottom: 18px; position: relative;
}
.carousel-inner {
	width: 100%; overflow: hidden; position: relative;
}
.carousel .item {
	transition:left 0.6s ease-in-out; display: none; position: relative; -webkit-transition: 0.6s ease-in-out left; -moz-transition: 0.6s ease-in-out left; -o-transition: 0.6s ease-in-out left;
}
.carousel .item > img {
	line-height: 1; display: block;
}
.carousel .active {
	display: block;
}
.carousel .next {
	display: block;
}
.carousel .prev {
	display: block;
}
.carousel .active {
	left: 0px;
}
.carousel .next {
	top: 0px; width: 100%; position: absolute;
}
.carousel .prev {
	top: 0px; width: 100%; position: absolute;
}
.carousel .next {
	left: 100%;
}
.carousel .prev {
	left: -100%;
}
.carousel .left.next {
	left: 0px;
}
.carousel .right.prev {
	left: 0px;
}
.carousel .left.active {
	left: -100%;
}
.carousel .right.active {
	left: 100%;
}
.carousel-control {
	background: rgb(34, 34, 34); border-radius: 23px; border: 3px solid rgb(255, 255, 255); border-image: none; left: 15px; top: 40%; width: 40px; height: 40px; text-align: center; color: rgb(255, 255, 255); line-height: 30px; font-size: 60px; font-weight: 100; margin-top: -20px; position: absolute; opacity: 0.5; -webkit-border-radius: 23px; -moz-border-radius: 23px;
}
.right.carousel-control {
	left: auto; right: 15px;
}
.carousel-control:hover {
	color: rgb(255, 255, 255); text-decoration: none; opacity: 0.9;
}
.carousel-caption {
	background: rgba(0, 0, 0, 0.75); padding: 10px 15px 5px; left: 0px; right: 0px; bottom: 0px; position: absolute;
}
.carousel-caption h4 {
	color: rgb(255, 255, 255);
}
.carousel-caption p {
	color: rgb(255, 255, 255);
}
.hero-unit {
	padding: 60px; border-radius: 6px; margin-bottom: 30px; background-color: rgb(238, 238, 238); -webkit-border-radius: 6px; -moz-border-radius: 6px;
}
.hero-unit h1 {
	color: inherit; line-height: 1; letter-spacing: -1px; font-size: 60px; margin-bottom: 0px;
}
.hero-unit p {
	color: inherit; line-height: 27px; font-size: 18px; font-weight: 200;
}
.pull-right {
	float: right;
}
.pull-left {
	float: left;
}
.hide {
	display: none;
}
.show {
	display: block;
}
.invisible {
	visibility: hidden;
}
