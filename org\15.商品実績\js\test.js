var itemFlag = 0;
var itemGroups_1 = [ [1,13,false],[14,23,false],[24,33,false]];
var itemGroups_2 = [ [1,7,false],[8,16,false],[17,27,false]];

function concealItem(id) {

  itemArray = new Array();
  items = document.getElementById(id).all('item');
  j = 0 ;
  for(i = 0 ; i < items.length ; i++) {
    if(items[i].checked){
      itemArray[j] = items[i].value;
      j++ ;
    }
  }
  elements = document.getElementById(id).all.tags('td');
  for(i = 0 ; i < elements.length ; i++) {
    for(j = 0 ; j < itemArray.length ; j++) {
      var id = "col_"+itemArray[j];
      if(elements[i].id == id ) {
        elements[i].style.display = "none" ;
      }
    }
  }
}

function appearAllItem(id) {
  elements = document.getElementById(id).all.tags('td');
  for(i = 0 ; i < elements.length ; i++) {
    if(elements[i].style.display == "none" ) {
      elements[i].style.display = "" ;
    }
  }
}

function appearItem(id) {
  appearAllItem(id);
  if(id == 'ToggleTable'){
    for(i = 0 ; i < this.itemGroups_2.length; i ++){
      if(this.itemGroups_2[i][2]){
        toggle(i);
      }
    }
  }else{
    blockItems(this.itemFlag);
  }
}

function toggleStyle(prefix){
  arrays = (prefix == 'ex_') ? this.itemGroups_1 : this.itemGroups_2 ;
  for(i = 0 ; i < arrays.length; i ++){
    if(this.arrays[i][2]){
      document.getElementById(prefix+'button_'+i).className = "item_selected";
    }else{
      document.getElementById(prefix+'button_'+i).className = "item";
    }
  }

}
function blockItems(groupId) {
  for(i = 0 ; i < this.itemGroups_1.length; i ++){
    if(i == groupId){
      this.itemGroups_1[i][2] = true;
    }else{
      this.itemGroups_1[i][2] = false;
    }
  }
  toggleStyle('ex_');

  this.itemFlag = groupId;
  var start = this.itemGroups_1[groupId][0];
  var end = this.itemGroups_1[groupId][1];
  appearAllItem('ExTable');
  items = document.getElementById('ExTable').all('item');
  flag = false;
  for(i = 0 ; i < items.length ; i++) {
    if(items[i].value == start){
      flag = true;
    }
    if(flag) {
      items[i].checked = true ;
      if(items[i].value == end ){
        flag = false;
      }
    }else{
      items[i].checked = false ;
    }
  }
  concealItem('ExTable');  
}

function toggleItems(groupId){
   this.itemGroups_2[groupId][2] = !this.itemGroups_2[groupId][2];
   toggleStyle('tg_');
   toggle(groupId);
}

function toggle(groupId) {
  appearAllItem('ToggleTable');
  items = document.getElementById('ToggleTable').all('item');
  start = this.itemGroups_2[groupId][0];
  end = this.itemGroups_2[groupId][1];
  var flag = false;
  for(j = 0 ; j < items.length ; j++) {
    if(items[j].value == start){
      flag = true;
    }
    if(flag){
      if(this.itemGroups_2[groupId][2]) {
        items[j].checked = true ;
      }else{
        items[j].checked = false ;
      }
      if(items[j].value == end ){
        break;
      }
    }
  }
  concealItem('ToggleTable');  
}

function setTeikeiEntry2(url) {
  var attributeStr = "dialogLeft=0;dialogTop=0;dialogWidth:500px; dialogHeight:500px;resizable:0,scroll:1;status=0";
  var modalAttributeStr = "scrollbars=no;status=no;resizable=no";
  var returnVal = window.showModalDialog(url, "", attributeStr, modalAttributeStr);
  return returnVal;
}

function setTeikeiList2(url) {
  var attributeStr = "dialogLeft=0;dialogTop=0;dialogWidth:900px; dialogHeight:600px;resizable:0,scroll:1;status=0";
  var modalAttributeStr = "scrollbars=no;status=no;resizable=no";
  var returnVal = window.showModalDialog(url, "", attributeStr, modalAttributeStr);
  return returnVal;
}